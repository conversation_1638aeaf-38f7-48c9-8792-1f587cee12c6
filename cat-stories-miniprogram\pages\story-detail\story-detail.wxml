<!-- pages/story-detail/story-detail.wxml -->
<view class="container theme-{{theme}}">
  <!-- 主题切换按钮 -->
  <view class="theme-toggle" bindtap="toggleTheme">
    {{theme === 'light' ? '🌙' : '☀️'}}
  </view>

  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    ← 返回故事列表
  </view>

  <view wx:if="{{story}}" class="story-detail">
    <!-- 故事标题 -->
    <view class="story-header">
      <view class="story-title">{{story.title}}</view>
      
      <!-- 大图展示 -->
      <view class="story-image-large image-container">
        <view class="image-placeholder">
          <view class="story-emoji-large">{{story.emoji}}</view>
          <view class="image-placeholder-text">Eric <PERSON>风格全尺寸插画</view>
        </view>
      </view>
    </view>

    <!-- 故事内容 -->
    <view class="story-content">
      <text class="story-text">{{story.content}}</text>
    </view>

    <!-- 操作按钮 -->
    <view class="story-actions">
      <button class="favorite-btn large" bindtap="toggleFavorite">
        {{isFavorite ? '❤️ 已收藏' : '🤍 加入收藏'}}
      </button>

      <button class="btn" bindtap="shareStory">
        📤 分享给朋友
      </button>

      <button class="btn" bindtap="randomNext">
        🎲 随机阅读
      </button>
    </view>

    <!-- 导航到其他故事 -->
    <view class="story-navigation">
      <button 
        wx:if="{{prevStory}}" 
        class="nav-btn prev" 
        bindtap="goToStory" 
        data-id="{{prevStory.id}}"
      >
        <view class="nav-emoji">{{prevStory.emoji}}</view>
        <view class="nav-text">
          <view class="nav-label">← 上一个故事</view>
          <view class="nav-title">{{prevStory.title}}</view>
        </view>
      </button>
      
      <button 
        wx:if="{{nextStory}}" 
        class="nav-btn next" 
        bindtap="goToStory" 
        data-id="{{nextStory.id}}"
      >
        <view class="nav-text">
          <view class="nav-label">下一个故事 →</view>
          <view class="nav-title">{{nextStory.title}}</view>
        </view>
        <view class="nav-emoji">{{nextStory.emoji}}</view>
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:else class="loading">
    <view class="loading-emoji">🐱</view>
    <view class="loading-text">加载中...</view>
  </view>
</view>
