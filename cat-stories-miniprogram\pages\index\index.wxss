/* pages/index/index.wxss */

/* 操作按钮区域 - 温馨网格布局 */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 64rpx;
  padding: 0 8rpx;
  animation: fadeIn 0.6s ease-in;
}

.action-buttons .btn {
  min-height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-buttons .btn:hover {
  transform: translateY(-4rpx) scale(1.02);
}

/* 按钮激活动画 - 温馨脉冲效果 */
.action-buttons .btn.btn-primary {
  animation: buttonPulse 0.8s ease-out;
}

@keyframes buttonPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(230, 126, 34, 0.3), 0 0 0 0 rgba(230, 126, 34, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 8rpx 24rpx rgba(230, 126, 34, 0.4), 0 0 0 12rpx rgba(230, 126, 34, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(230, 126, 34, 0.3), 0 0 0 0 rgba(230, 126, 34, 0);
  }
}

/* 特稿样式 */
.featured-label {
  font-size: 24rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2rpx;
  color: var(--secondary-text);
  margin-bottom: 16rpx;
}

.featured-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.featured-text {
  flex: 1;
}

.featured-title {
  font-size: 64rpx;
  font-weight: 900;
  line-height: 1.2;
  margin-bottom: 32rpx;
  color: var(--text-color);
}

.featured-excerpt {
  font-size: 32rpx;
  line-height: 1.6;
  color: var(--secondary-text);
  margin-bottom: 48rpx;
}

.featured-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.featured-image {
  height: 600rpx;
}

.featured-emoji {
  font-size: 200rpx;
}

/* 故事卡片 */
.story-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: var(--text-color);
}

.story-excerpt {
  font-size: 28rpx;
  line-height: 1.5;
  color: var(--secondary-text);
  margin-bottom: 32rpx;
}

.story-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-more {
  font-size: 24rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1rpx;
  color: var(--text-color);
}

/* 无结果样式 */
.no-results {
  text-align: center;
  padding: 128rpx 32rpx;
}

.no-results-emoji {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.no-results-text {
  font-size: 32rpx;
  color: var(--secondary-text);
  margin-bottom: 48rpx;
}

/* 响应式布局 */
@media (min-width: 768rpx) {
  .featured-content {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .featured-text {
    flex: 2;
  }
  
  .featured-image {
    flex: 3;
    margin-left: 64rpx;
  }
  
  .action-buttons .btn {
    flex: none;
    min-width: 200rpx;
  }
}

/* 响应式设计 - 优化小屏幕体验 */
@media (max-width: 768rpx) {
  .action-buttons {
    grid-template-columns: 1fr;
    gap: 16rpx;
    padding: 0 16rpx;
  }

  .action-buttons .btn {
    min-height: 80rpx;
    font-size: 24rpx;
  }
}

/* 温馨装饰元素 */
.page-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: linear-gradient(90deg,
    var(--accent-color) 0%,
    var(--warm-orange) 25%,
    var(--accent-light) 50%,
    var(--warm-orange) 75%,
    var(--accent-color) 100%);
  z-index: 1000;
  opacity: 0.8;
}

/* 加载状态优化 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: var(--secondary-text);
  font-size: 28rpx;
}

.loading-indicator::before {
  content: '🐱';
  margin-right: 16rpx;
  animation: bounce 1.5s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
