/* pages/index/index.wxss */

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: center;
  margin-bottom: 64rpx;
}

.action-buttons .btn {
  flex: 1;
  min-width: 160rpx;
  text-align: center;
}

/* 特稿样式 */
.featured-label {
  font-size: 24rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2rpx;
  color: var(--secondary-text);
  margin-bottom: 16rpx;
}

.featured-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.featured-text {
  flex: 1;
}

.featured-title {
  font-size: 64rpx;
  font-weight: 900;
  line-height: 1.2;
  margin-bottom: 32rpx;
  color: var(--text-color);
}

.featured-excerpt {
  font-size: 32rpx;
  line-height: 1.6;
  color: var(--secondary-text);
  margin-bottom: 48rpx;
}

.featured-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.featured-image {
  height: 600rpx;
}

.featured-emoji {
  font-size: 200rpx;
}

/* 故事卡片 */
.story-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: var(--text-color);
}

.story-excerpt {
  font-size: 28rpx;
  line-height: 1.5;
  color: var(--secondary-text);
  margin-bottom: 32rpx;
}

.story-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-more {
  font-size: 24rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1rpx;
  color: var(--text-color);
}

/* 无结果样式 */
.no-results {
  text-align: center;
  padding: 128rpx 32rpx;
}

.no-results-emoji {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.no-results-text {
  font-size: 32rpx;
  color: var(--secondary-text);
  margin-bottom: 48rpx;
}

/* 响应式布局 */
@media (min-width: 768rpx) {
  .featured-content {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .featured-text {
    flex: 2;
  }
  
  .featured-image {
    flex: 3;
    margin-left: 64rpx;
  }
  
  .action-buttons .btn {
    flex: none;
    min-width: 200rpx;
  }
}
