import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "可爱猫咪故事集 🐾",
  description: "欢迎来到温馨的猫咪世界，这里有最可爱的猫咪故事等着你来发现！",
  keywords: ["猫咪", "故事", "可爱", "宠物", "温馨"],
  authors: [{ name: "Cat Stories Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
