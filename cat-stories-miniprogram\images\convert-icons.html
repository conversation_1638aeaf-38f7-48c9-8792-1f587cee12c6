<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar图标转换工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-item h3 {
            margin: 0 0 15px 0;
            color: #555;
        }
        .icon-preview {
            width: 81px;
            height: 81px;
            margin: 0 auto 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .download-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .download-btn:hover {
            background: #0056CC;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
            margin-top: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0056CC;
        }
        .instructions ol {
            margin: 15px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐱 猫咪故事杂志 - TabBar图标转换</h1>
        
        <div class="icon-grid">
            <div class="icon-item">
                <h3>首页图标</h3>
                <div class="icon-preview" id="home-preview"></div>
                <button class="download-btn" onclick="downloadIcon('home')">下载未选中</button>
                <button class="download-btn" onclick="downloadIcon('home-active')">下载选中</button>
            </div>
            
            <div class="icon-item">
                <h3>目录图标</h3>
                <div class="icon-preview" id="list-preview"></div>
                <button class="download-btn" onclick="downloadIcon('list')">下载未选中</button>
                <button class="download-btn" onclick="downloadIcon('list-active')">下载选中</button>
            </div>
            
            <div class="icon-item">
                <h3>封面图标</h3>
                <div class="icon-preview" id="cover-preview"></div>
                <button class="download-btn" onclick="downloadIcon('cover')">下载未选中</button>
                <button class="download-btn" onclick="downloadIcon('cover-active')">下载选中</button>
            </div>
        </div>

        <!-- 批量下载按钮 -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="download-btn" onclick="downloadAllIcons()" style="background: #007AFF; color: white; font-size: 16px; padding: 12px 24px;">
                📦 一键下载全部图标
            </button>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>下载图标：</strong>点击上方按钮下载对应的PNG图标文件</li>
                <li><strong>文件命名：</strong>下载的文件会自动命名为正确的格式（如 home.png, home-active.png）</li>
                <li><strong>放置位置：</strong>将下载的PNG文件放入小程序的 <code>/images/</code> 目录</li>
                <li><strong>图标规格：</strong>所有图标都是 81×81 像素，符合微信小程序TabBar要求</li>
                <li><strong>替代方案：</strong>如果下载功能不工作，可以使用在线SVG转PNG工具转换SVG文件</li>
            </ol>
            
            <h3>🔧 替代转换方法</h3>
            <p>如果自动下载不工作，可以使用以下在线工具：</p>
            <ul>
                <li><a href="https://convertio.co/zh/svg-png/" target="_blank">Convertio SVG转PNG</a></li>
                <li><a href="https://www.aconvert.com/cn/image/svg-to-png/" target="_blank">AConvert SVG转PNG</a></li>
                <li><a href="https://cloudconvert.com/svg-to-png" target="_blank">CloudConvert SVG转PNG</a></li>
            </ul>
            <p>转换时请确保输出尺寸设置为 <strong>81×81 像素</strong>。</p>
        </div>
    </div>

    <script>
        // SVG内容
        const svgContent = {
            'home': `<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="35" width="41" height="35" stroke="#666666" stroke-width="3" fill="none"/>
                <path d="M15 40 L40.5 20 L66 40" stroke="#666666" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                <rect x="35" y="50" width="11" height="20" stroke="#666666" stroke-width="2" fill="none"/>
                <circle cx="38" cy="55" r="1.5" fill="#666666"/>
            </svg>`,
            'home-active': `<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="35" width="41" height="35" stroke="#000000" stroke-width="3" fill="#000000"/>
                <path d="M15 40 L40.5 20 L66 40" stroke="#000000" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                <rect x="35" y="50" width="11" height="20" stroke="#ffffff" stroke-width="2" fill="#ffffff"/>
                <circle cx="38" cy="55" r="1.5" fill="#000000"/>
            </svg>`,
            'list': `<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="15" y="20" width="51" height="8" stroke="#666666" stroke-width="2" fill="none"/>
                <rect x="15" y="36" width="51" height="8" stroke="#666666" stroke-width="2" fill="none"/>
                <rect x="15" y="52" width="51" height="8" stroke="#666666" stroke-width="2" fill="none"/>
                <circle cx="20" cy="24" r="2" fill="#666666"/>
                <circle cx="20" cy="40" r="2" fill="#666666"/>
                <circle cx="20" cy="56" r="2" fill="#666666"/>
                <line x1="28" y1="24" x2="58" y2="24" stroke="#666666" stroke-width="1.5"/>
                <line x1="28" y1="40" x2="58" y2="40" stroke="#666666" stroke-width="1.5"/>
                <line x1="28" y1="56" x2="58" y2="56" stroke="#666666" stroke-width="1.5"/>
            </svg>`,
            'list-active': `<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="15" y="20" width="51" height="8" stroke="#000000" stroke-width="2" fill="#000000"/>
                <rect x="15" y="36" width="51" height="8" stroke="#000000" stroke-width="2" fill="#000000"/>
                <rect x="15" y="52" width="51" height="8" stroke="#000000" stroke-width="2" fill="#000000"/>
                <circle cx="20" cy="24" r="2" fill="#ffffff"/>
                <circle cx="20" cy="40" r="2" fill="#ffffff"/>
                <circle cx="20" cy="56" r="2" fill="#ffffff"/>
                <line x1="28" y1="24" x2="58" y2="24" stroke="#ffffff" stroke-width="1.5"/>
                <line x1="28" y1="40" x2="58" y2="40" stroke="#ffffff" stroke-width="1.5"/>
                <line x1="28" y1="56" x2="58" y2="56" stroke="#ffffff" stroke-width="1.5"/>
            </svg>`,
            'cover': `<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="15" width="41" height="51" stroke="#666666" stroke-width="3" fill="none"/>
                <rect x="25" y="20" width="31" height="8" fill="#666666"/>
                <rect x="25" y="32" width="31" height="20" stroke="#666666" stroke-width="2" fill="none"/>
                <circle cx="40.5" cy="40" r="3" stroke="#666666" stroke-width="1.5" fill="none"/>
                <circle cx="38.5" cy="38.5" r="0.8" fill="#666666"/>
                <circle cx="42.5" cy="38.5" r="0.8" fill="#666666"/>
                <path d="M38 43 Q40.5 44.5 43 43" stroke="#666666" stroke-width="1" fill="none"/>
                <line x1="25" y1="56" x2="56" y2="56" stroke="#666666" stroke-width="1"/>
                <line x1="25" y1="59" x2="50" y2="59" stroke="#666666" stroke-width="1"/>
            </svg>`,
            'cover-active': `<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="15" width="41" height="51" stroke="#000000" stroke-width="3" fill="#ffffff"/>
                <rect x="25" y="20" width="31" height="8" fill="#000000"/>
                <rect x="25" y="32" width="31" height="20" stroke="#000000" stroke-width="2" fill="#000000"/>
                <circle cx="40.5" cy="40" r="3" stroke="#ffffff" stroke-width="1.5" fill="none"/>
                <circle cx="38.5" cy="38.5" r="0.8" fill="#ffffff"/>
                <circle cx="42.5" cy="38.5" r="0.8" fill="#ffffff"/>
                <path d="M38 43 Q40.5 44.5 43 43" stroke="#ffffff" stroke-width="1" fill="none"/>
                <line x1="25" y1="56" x2="56" y2="56" stroke="#000000" stroke-width="1"/>
                <line x1="25" y1="59" x2="50" y2="59" stroke="#000000" stroke-width="1"/>
            </svg>`
        };

        // 页面加载时显示预览
        window.onload = function() {
            document.getElementById('home-preview').innerHTML = svgContent['home'];
            document.getElementById('list-preview').innerHTML = svgContent['list'];
            document.getElementById('cover-preview').innerHTML = svgContent['cover'];
        };

        // 下载图标函数
        function downloadIcon(iconName) {
            const svg = svgContent[iconName];
            if (!svg) return;

            // 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = 81;
            canvas.height = 81;
            const ctx = canvas.getContext('2d');

            // 设置白色背景（透明背景）
            ctx.clearRect(0, 0, 81, 81);

            // 创建图片
            const img = new Image();
            const svgBlob = new Blob([svg], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);

            img.onload = function() {
                try {
                    // 绘制到canvas
                    ctx.drawImage(img, 0, 0, 81, 81);

                    // 转换为PNG并下载
                    canvas.toBlob(function(blob) {
                        if (blob) {
                            const link = document.createElement('a');
                            link.download = iconName + '.png';
                            link.href = URL.createObjectURL(blob);
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            // 清理
                            URL.revokeObjectURL(link.href);
                            console.log(`✅ Downloaded ${iconName}.png`);
                        } else {
                            console.error(`❌ Failed to create blob for ${iconName}`);
                        }
                    }, 'image/png');
                } catch (error) {
                    console.error(`❌ Error processing ${iconName}:`, error);
                } finally {
                    // 清理
                    URL.revokeObjectURL(url);
                }
            };

            img.onerror = function() {
                console.error(`❌ Failed to load SVG for ${iconName}`);
                URL.revokeObjectURL(url);
            };

            img.src = url;
        }

        // 下载所有图标
        function downloadAllIcons() {
            const iconNames = Object.keys(svgContent);
            let index = 0;

            function downloadNext() {
                if (index < iconNames.length) {
                    const iconName = iconNames[index];
                    console.log(`🔄 Processing ${iconName}...`);
                    downloadIcon(iconName);
                    index++;
                    // 延迟一点时间再下载下一个
                    setTimeout(downloadNext, 1000);
                } else {
                    console.log('🎉 All icons processed!');
                }
            }

            downloadNext();
        }
    </script>
</body>
</html>
