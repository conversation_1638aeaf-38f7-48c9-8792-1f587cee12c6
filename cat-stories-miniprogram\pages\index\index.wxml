<!-- pages/index/index.wxml -->
<view class="container theme-{{theme}}">
  <!-- 主题切换按钮 -->
  <view class="theme-toggle" bindtap="toggleTheme">
    {{theme === 'light' ? '🌙' : '☀️'}}
  </view>

  <!-- 杂志标题 -->
  <view class="magazine-title">猫咪故事杂志</view>
  <view class="magazine-subtitle">温馨可爱的猫咪世界</view>

  <!-- 搜索框 -->
  <view class="search-container">
    <input 
      class="search-input" 
      placeholder="搜索故事..." 
      value="{{searchQuery}}"
      bindinput="onSearchInput"
    />
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn" bindtap="goToTableOfContents">目录</button>
    <button class="btn" bindtap="goToMagazineCover">封面</button>
    <button class="btn btn-primary" bindtap="randomStory">随机阅读</button>
    <button class="btn {{showFavoritesOnly ? 'btn-primary' : ''}}" bindtap="toggleFavoritesFilter">
      {{showFavoritesOnly ? '显示全部' : '我的收藏'}} ({{favorites.length}})
    </button>
  </view>

  <!-- 特稿故事 (第一页的第一个故事) -->
  <view wx:if="{{currentPage === 0 && currentStories.length > 0}}" class="featured-story">
    <view class="featured-label">本期特稿</view>
    <view class="featured-content">
      <view class="featured-text">
        <view class="featured-title">{{currentStories[0].title}}</view>
        <view class="featured-excerpt">{{currentStories[0].content.substring(0, 200)}}...</view>
        <view class="featured-actions">
          <button class="btn btn-primary" bindtap="readStory" data-story="{{currentStories[0]}}">
            阅读全文
          </button>
          <button class="favorite-btn" bindtap="toggleFavorite" data-id="{{currentStories[0].id}}">
            {{isFavorite(currentStories[0].id) ? '❤️' : '🤍'}}
          </button>
        </view>
      </view>
      <view class="featured-image image-container">
        <view class="image-placeholder">
          <view class="story-emoji featured-emoji">{{currentStories[0].emoji}}</view>
          <view class="image-placeholder-text">Eric Carle风格封面插画</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 其他故事网格 -->
  <view class="stories-grid">
    <view 
      wx:for="{{currentStories.slice(currentPage === 0 ? 1 : 0)}}" 
      wx:key="id"
      class="story-card"
      bindtap="readStory"
      data-story="{{item}}"
    >
      <view class="image-container">
        <view class="image-placeholder">
          <view class="story-emoji">{{item.emoji}}</view>
          <view class="image-placeholder-text">Eric Carle风格插画</view>
        </view>
      </view>
      
      <view class="story-title">{{item.title}}</view>
      <view class="story-excerpt">{{item.content.substring(0, 120)}}...</view>
      
      <view class="story-actions">
        <view class="read-more">阅读更多 →</view>
        <button class="favorite-btn" bindtap="toggleFavorite" data-id="{{item.id}}" catchtap="true">
          {{isFavorite(item.id) ? '❤️' : '🤍'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 分页 -->
  <view class="pagination" wx:if="{{totalPages > 1}}">
    <button 
      class="pagination-btn" 
      bindtap="prevPage" 
      disabled="{{currentPage === 0}}"
    >
      ← 上一页
    </button>
    
    <view class="pagination-info">
      第 {{currentPage + 1}} 页 / 共 {{totalPages}} 页
    </view>
    
    <button 
      class="pagination-btn" 
      bindtap="nextPage" 
      disabled="{{currentPage === totalPages - 1}}"
    >
      下一页 →
    </button>
  </view>

  <!-- 无结果提示 -->
  <view wx:if="{{currentStories.length === 0}}" class="no-results">
    <view class="no-results-emoji">😿</view>
    <view class="no-results-text">
      {{searchQuery ? '没有找到相关故事' : '暂无收藏的故事'}}
    </view>
    <button wx:if="{{searchQuery || showFavoritesOnly}}" class="btn" bindtap="clearFilters">
      清除筛选
    </button>
  </view>
</view>
