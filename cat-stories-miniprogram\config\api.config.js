/**
 * API 配置文件
 * 管理不同环境的API配置
 */

// 环境配置
const ENV_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'https://dev-api.catstories.com',
    timeout: 10000,
    enableMock: true, // 是否启用Mock数据
    logLevel: 'debug'
  },
  
  // 测试环境
  testing: {
    baseURL: 'https://test-api.catstories.com',
    timeout: 15000,
    enableMock: false,
    logLevel: 'info'
  },
  
  // 生产环境
  production: {
    baseURL: 'https://api.catstories.com',
    timeout: 15000,
    enableMock: false,
    logLevel: 'error'
  }
};

// 当前环境（可以通过构建工具动态设置）
const CURRENT_ENV = 'development'; // development | testing | production

// 获取当前环境配置
const getCurrentConfig = () => {
  return ENV_CONFIG[CURRENT_ENV] || ENV_CONFIG.development;
};

// API 端点配置
const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    WX_LOGIN: '/api/auth/wx-login',
    LOGOUT: '/api/auth/logout',
    REFRESH_TOKEN: '/api/auth/refresh'
  },
  
  // 用户相关
  USER: {
    INFO: '/api/user/info',
    UPDATE: '/api/user/info',
    STATS: '/api/user/stats',
    FAVORITES: '/api/user/favorites',
    READING_HISTORY: '/api/user/reading-history',
    SYNC: '/api/user/sync'
  },
  
  // 故事相关
  STORY: {
    LIST: '/api/stories',
    DETAIL: '/api/stories/:id',
    SEARCH: '/api/stories/search',
    RECOMMENDED: '/api/stories/recommended',
    POPULAR: '/api/stories/popular',
    CATEGORIES: '/api/stories/categories',
    READ: '/api/stories/read'
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: '/api/upload/image',
    AVATAR: '/api/upload/avatar'
  }
};

// Mock 数据配置
const MOCK_CONFIG = {
  enabled: getCurrentConfig().enableMock,
  delay: 1000, // Mock 请求延迟（毫秒）
  
  // Mock 数据
  data: {
    // 用户信息
    userInfo: {
      id: 1,
      nickname: '猫咪爱好者',
      avatar: 'https://example.com/avatar.jpg',
      level: 5,
      readCount: 15,
      favoriteCount: 8
    },
    
    // 故事列表
    stories: [
      {
        id: 1,
        title: '小橘猫的冒险',
        emoji: '🐱',
        category: '冒险',
        author: '猫咪故事杂志',
        readCount: 1250,
        favoriteCount: 89,
        publishTime: '2024-01-15',
        content: '在一个阳光明媚的早晨...',
        tags: ['温馨', '冒险', '成长']
      }
      // 更多故事...
    ],
    
    // 分类列表
    categories: [
      { id: 1, name: '冒险', icon: '🗺️', count: 15 },
      { id: 2, name: '温馨', icon: '❤️', count: 20 },
      { id: 3, name: '搞笑', icon: '😸', count: 12 },
      { id: 4, name: '治愈', icon: '🌈', count: 18 }
    ]
  }
};

// 错误码配置
const ERROR_CODES = {
  // 通用错误
  SUCCESS: 0,
  UNKNOWN_ERROR: -1,
  NETWORK_ERROR: -2,
  TIMEOUT_ERROR: -3,
  
  // 认证错误
  AUTH_FAILED: 1001,
  TOKEN_EXPIRED: 1002,
  TOKEN_INVALID: 1003,
  PERMISSION_DENIED: 1004,
  
  // 业务错误
  STORY_NOT_FOUND: 2001,
  USER_NOT_FOUND: 2002,
  FAVORITE_EXISTS: 2003,
  FAVORITE_NOT_EXISTS: 2004,
  
  // 参数错误
  PARAM_MISSING: 3001,
  PARAM_INVALID: 3002,
  PARAM_FORMAT_ERROR: 3003
};

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时',
  [ERROR_CODES.AUTH_FAILED]: '登录失败',
  [ERROR_CODES.TOKEN_EXPIRED]: '登录已过期',
  [ERROR_CODES.TOKEN_INVALID]: '登录状态无效',
  [ERROR_CODES.PERMISSION_DENIED]: '权限不足',
  [ERROR_CODES.STORY_NOT_FOUND]: '故事不存在',
  [ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
  [ERROR_CODES.FAVORITE_EXISTS]: '已经收藏过了',
  [ERROR_CODES.FAVORITE_NOT_EXISTS]: '收藏不存在',
  [ERROR_CODES.PARAM_MISSING]: '缺少必要参数',
  [ERROR_CODES.PARAM_INVALID]: '参数无效',
  [ERROR_CODES.PARAM_FORMAT_ERROR]: '参数格式错误'
};

// 请求配置
const REQUEST_CONFIG = {
  // 默认请求头
  defaultHeaders: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Client-Type': 'miniprogram',
    'X-Client-Version': '1.0.0'
  },
  
  // 重试配置
  retry: {
    times: 3,
    delay: 1000,
    codes: [500, 502, 503, 504] // 需要重试的HTTP状态码
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    duration: 5 * 60 * 1000, // 5分钟
    keys: ['stories', 'categories'] // 需要缓存的接口
  }
};

export {
  ENV_CONFIG,
  CURRENT_ENV,
  getCurrentConfig,
  API_ENDPOINTS,
  MOCK_CONFIG,
  ERROR_CODES,
  ERROR_MESSAGES,
  REQUEST_CONFIG
};
