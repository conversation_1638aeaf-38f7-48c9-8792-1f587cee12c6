/**
 * 用户相关API服务
 */
import api from '../utils/api.js';

class UserService {
  /**
   * 微信登录
   * @param {string} code - 微信登录code
   */
  async wxLogin(code) {
    try {
      const response = await api.post('/api/auth/wx-login', {
        code
      }, {
        loadingText: '登录中...'
      });

      if (response.success) {
        // 保存用户信息和token
        const { token, userInfo } = response.data;
        api.setToken(token);
        wx.setStorageSync('userInfo', userInfo);
        
        return {
          success: true,
          data: response.data
        };
      }
    } catch (error) {
      console.error('微信登录失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      const response = await api.get('/api/user/info');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 更新用户信息
   * @param {Object} userInfo - 用户信息
   */
  async updateUserInfo(userInfo) {
    try {
      const response = await api.put('/api/user/info', userInfo);
      
      if (response.success) {
        // 更新本地用户信息
        wx.setStorageSync('userInfo', response.data);
      }
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户收藏列表
   * @param {Object} params - 查询参数
   */
  async getFavorites(params = {}) {
    try {
      const response = await api.get('/api/user/favorites', params);
      return {
        success: true,
        data: response.data,
        total: response.total
      };
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 添加收藏
   * @param {number} storyId - 故事ID
   */
  async addFavorite(storyId) {
    try {
      const response = await api.post('/api/user/favorites', {
        storyId
      }, {
        showLoading: false
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('添加收藏失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 取消收藏
   * @param {number} storyId - 故事ID
   */
  async removeFavorite(storyId) {
    try {
      const response = await api.delete(`/api/user/favorites/${storyId}`, {
        showLoading: false
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('取消收藏失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取阅读历史
   * @param {Object} params - 查询参数
   */
  async getReadingHistory(params = {}) {
    try {
      const response = await api.get('/api/user/reading-history', params);
      return {
        success: true,
        data: response.data,
        total: response.total
      };
    } catch (error) {
      console.error('获取阅读历史失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats() {
    try {
      const response = await api.get('/api/user/stats');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取用户统计失败:', error);
      return {
        success: false,
        error: error.message,
        data: {
          totalRead: 0,
          totalFavorites: 0,
          readingDays: 0
        }
      };
    }
  }

  /**
   * 同步本地数据到服务器
   * @param {Object} localData - 本地数据
   */
  async syncLocalData(localData) {
    try {
      const response = await api.post('/api/user/sync', localData, {
        showLoading: false
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('同步数据失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 退出登录
   */
  async logout() {
    try {
      await api.post('/api/auth/logout', {}, {
        showLoading: false
      });
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      // 清除本地数据
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      api.setToken('');
    }
  }
}

// 创建服务实例
const userService = new UserService();

export default userService;
