interface CatIconProps {
  className?: string;
  size?: number;
}

export default function CatIcon({ className = "", size = 24 }: CatIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Cat face */}
      <circle cx="12" cy="14" r="8" fill="#FFA500" stroke="#FF8C00" strokeWidth="1"/>
      
      {/* Cat ears */}
      <path d="M6 8 L8 4 L10 8 Z" fill="#FFA500" stroke="#FF8C00" strokeWidth="1"/>
      <path d="M14 8 L16 4 L18 8 Z" fill="#FFA500" stroke="#FF8C00" strokeWidth="1"/>
      
      {/* Inner ears */}
      <path d="M7 7 L8 5 L9 7 Z" fill="#FFB84D"/>
      <path d="M15 7 L16 5 L17 7 Z" fill="#FFB84D"/>
      
      {/* Eyes */}
      <circle cx="9" cy="12" r="1.5" fill="#000"/>
      <circle cx="15" cy="12" r="1.5" fill="#000"/>
      <circle cx="9.5" cy="11.5" r="0.5" fill="#FFF"/>
      <circle cx="15.5" cy="11.5" r="0.5" fill="#FFF"/>
      
      {/* Nose */}
      <path d="M12 14 L11 15 L13 15 Z" fill="#FF69B4"/>
      
      {/* Mouth */}
      <path d="M12 15 Q10 17 8 16" stroke="#000" strokeWidth="1" fill="none"/>
      <path d="M12 15 Q14 17 16 16" stroke="#000" strokeWidth="1" fill="none"/>
      
      {/* Whiskers */}
      <line x1="5" y1="13" x2="8" y2="13" stroke="#000" strokeWidth="1"/>
      <line x1="5" y1="15" x2="8" y2="14" stroke="#000" strokeWidth="1"/>
      <line x1="16" y1="13" x2="19" y2="13" stroke="#000" strokeWidth="1"/>
      <line x1="16" y1="14" x2="19" y2="15" stroke="#000" strokeWidth="1"/>
    </svg>
  );
}
