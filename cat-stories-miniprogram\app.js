// app.js
import dataManager from './utils/dataManager.js';
import userService from './services/userService.js';

App({
  globalData: {
    theme: 'light',
    favorites: [],
    readStories: [], // 已阅读的故事ID列表
    userInfo: null, // 用户信息
    isLoggedIn: false, // 登录状态
    stories: [
      {
        id: 1,
        title: "小橘猫的冒险",
        emoji: "🐱",
        content: "在一个阳光明媚的早晨，小橘猫咪咪从温暖的猫窝中醒来。它伸了个懒腰，打了个哈欠，然后跳到窗台上看着外面的世界。\n\n花园里，蝴蝶在花丛中翩翩起舞，小鸟在树枝上欢快地歌唱。咪咪的眼睛闪闪发光，它决定今天要去探索这个美丽的世界。\n\n咪咪轻巧地跳下窗台，穿过猫门，来到了花园里。它小心翼翼地走在石径上，鼻子不停地嗅着各种新鲜的气味。突然，一只彩色的蝴蝶飞到了它的面前。\n\n'你好，小猫咪！'蝴蝶用甜美的声音说道，'你想和我一起去看看花园的秘密吗？'\n\n咪咪兴奋地点点头，跟着蝴蝶开始了它的第一次大冒险。它们一起发现了隐藏在玫瑰丛后的小池塘，看到了在荷叶上休息的青蛙，还遇到了一只友善的刺猬。\n\n当太阳开始西下时，咪咪满足地回到了家，心中充满了对明天新冒险的期待。"
      },
      {
        id: 2,
        title: "月光下的白猫",
        emoji: "🌙",
        content: "夜幕降临，银色的月光洒在安静的小镇上。一只雪白的猫咪悄悄地从屋顶上走过，它的毛发在月光下闪闪发光，就像披着一件银色的斗篷。\n\n这只白猫名叫月儿，它有着一双深蓝色的眼睛，就像夜空中最亮的星星。每当月圆之夜，月儿就会开始它的夜间漫步，探索这个在白天看起来完全不同的世界。\n\n今晚，月儿沿着屋顶的边缘优雅地行走，它的爪子无声地踩在瓦片上。突然，它听到了一阵轻柔的音乐声，那是从街角的小咖啡馆传来的。\n\n月儿好奇地跳到咖啡馆的窗台上，透过玻璃看到里面有一位老爷爷在弹钢琴。音乐如流水般优美，让月儿不禁陶醉其中。\n\n老爷爷注意到了窗外的月儿，他微笑着向它招手。从那天起，每个月圆之夜，月儿都会来到这里，静静地听着美妙的音乐，成为了老爷爷最忠实的听众。\n\n在音乐的陪伴下，月儿度过了一个又一个美好的夜晚，它的心中充满了宁静与快乐。"
      },
      {
        id: 3,
        title: "花园里的小黑猫",
        emoji: "🌸",
        content: "在一个充满花香的春日，小黑猫露娜在奶奶的花园里快乐地玩耍。它的黑色毛发在阳光下闪着健康的光泽，绿色的眼睛充满了好奇和活力。\n\n花园里盛开着各种各样的花朵：红色的玫瑰、紫色的薰衣草、黄色的向日葵，还有粉色的樱花。露娜在花丛中穿梭，每一朵花都让它感到新奇。\n\n突然，露娜听到了嗡嗡的声音。它抬头一看，原来是一只小蜜蜂正在辛勤地采蜜。小蜜蜂看到露娜，友好地说：'你好，小黑猫！你愿意帮我一起照顾这些美丽的花朵吗？'\n\n露娜高兴地答应了。从那天起，它每天都会来到花园，帮助小蜜蜂赶走害虫，给花朵浇水。在它们的共同努力下，花园变得更加美丽。\n\n奶奶看到露娜如此爱护花园，非常高兴。她经常给露娜准备美味的小鱼干作为奖励。露娜在花园里找到了真正的快乐，也学会了责任和友谊的珍贵。\n\n每当夕阳西下，露娜就会坐在花园的中央，看着蝴蝶在花丛中飞舞，心中充满了满足和幸福。"
      },
      {
        id: 4,
        title: "爱睡觉的灰猫",
        emoji: "😴",
        content: "在一个温馨的小屋里，住着一只名叫咕咕的灰色猫咪。咕咕有一个特别的爱好——睡觉。它可以在任何地方、任何时间睡着，而且每次都睡得特别香甜。\n\n早晨，当阳光透过窗帘洒进房间时，咕咕会选择在温暖的阳光下打盹。它会蜷缩成一个毛茸茸的球，尾巴轻轻地盖在鼻子上，发出轻柔的呼噜声。\n\n中午时分，咕咕最喜欢的地方是客厅里那张柔软的沙发。它会找到最舒适的角落，四肢放松地伸展开来，享受午后的宁静时光。\n\n有时候，咕咕还会在主人的书桌上睡觉，就在键盘旁边。主人工作时，咕咕的呼噜声就像是最好的背景音乐，让整个房间都充满了温馨的氛围。\n\n邻居们都很羡慕咕咕的生活态度。它从不为小事烦恼，总是保持着平静和满足的心情。咕咕用它的方式告诉大家：有时候，放慢脚步，享受简单的快乐，也是一种智慧。\n\n每当夜晚来临，咕咕会跳到主人的床上，蜷缩在枕头旁边，陪伴主人进入甜美的梦乡。在梦里，咕咕会梦到无数个舒适的睡觉地点，那里永远都是温暖而安全的。"
      },
      {
        id: 5,
        title: "勇敢的虎斑猫",
        emoji: "🦸",
        content: "在小镇的东边，住着一只勇敢的虎斑猫，名叫托尼。它有着美丽的条纹毛发和一双炯炯有神的琥珀色眼睛。托尼不仅勇敢，还有一颗善良的心，总是乐于帮助需要帮助的小动物。\n\n一天，托尼听到了小鸟的求救声。原来，一只小鸟的宝宝掉进了深深的井里。其他动物都束手无策，但托尼没有放弃。它仔细观察了井的结构，然后勇敢地跳了下去。\n\n在井底，托尼找到了受伤的小鸟宝宝。它轻柔地叼起小鸟，然后利用井壁上的凹凸不平，一步一步地爬了上来。当它把小鸟安全地交还给鸟妈妈时，所有的动物都为它鼓掌。\n\n还有一次，小镇上发生了火灾。托尼不顾危险，冲进燃烧的房屋，救出了被困的小仓鼠一家。它的勇敢行为感动了整个小镇的居民。\n\n托尼从不认为自己是英雄，它只是觉得帮助别人是理所当然的事情。它的善良和勇敢影响了许多其他动物，大家都学会了互相帮助，互相关爱。\n\n小镇因为有了托尼而变得更加和谐美好。每当有困难发生时，大家都知道可以依靠这只勇敢的虎斑猫。托尼用自己的行动证明了，真正的勇敢不是不害怕，而是即使害怕也要去做正确的事情。"
      },
      {
        id: 6,
        title: "小花猫的鱼儿梦",
        emoji: "🐟",
        content: "小花猫咪咪有着一身美丽的花纹，就像艺术家精心绘制的画作。它最大的梦想就是能够抓到一条大鱼，因为它听说鱼儿是世界上最美味的食物。\n\n每天，咪咪都会坐在小河边，目不转睛地盯着水中游来游去的鱼儿。它看到金色的鲤鱼在水中优雅地游泳，银色的小鱼在阳光下闪闪发光，还有彩色的热带鱼在水草间穿梭。\n\n咪咪试过很多次去抓鱼，但每次都以失败告终。它的爪子刚一碰到水面，鱼儿们就迅速游走了。有时候，咪咪还会不小心掉进水里，弄得全身湿漉漉的。\n\n一天，一位善良的渔夫看到了咪咪的努力。他被咪咪的坚持感动了，决定帮助它实现梦想。渔夫教咪咪如何耐心等待，如何观察鱼儿的游泳规律。\n\n在渔夫的指导下，咪咪终于学会了正确的捕鱼技巧。当它第一次成功抓到一条小鱼时，高兴得跳了起来。那条鱼确实很美味，但更重要的是，咪咪学会了坚持和耐心的价值。\n\n从那以后，咪咪不仅成为了一只优秀的捕鱼猫，还把自己学到的技巧教给了其他的小猫。它明白了，分享知识和帮助别人实现梦想，比独自享受成功更加快乐。"
      },
      {
        id: 7,
        title: "音乐猫的钢琴梦",
        emoji: "🎹",
        content: "在音乐学院附近的小巷里，住着一只特别的猫咪，名叫莫扎特。它有着一身优雅的黑白相间的毛发，就像钢琴的黑白键一样。莫扎特对音乐有着天生的热爱和敏感。\n\n每天晚上，当音乐学院的学生们练习钢琴时，莫扎特都会悄悄地坐在窗外聆听。它能够分辨出不同的音符，甚至能够感受到音乐中的情感变化。\n\n一天晚上，音乐学院的老教授发现了这只特别的猫咪。他惊讶地发现，莫扎特居然能够跟着音乐的节拍摆动尾巴，甚至还会在听到美妙旋律时发出和谐的呼噜声。\n\n教授被莫扎特的音乐天赋深深打动，决定让它进入音乐学院。他为莫扎特特制了一架小钢琴，键盘的大小正好适合猫爪。\n\n令人惊讶的是，莫扎特很快就学会了弹奏简单的旋律。它的爪子在琴键上轻巧地跳跃，弹奏出的音乐虽然简单，但充满了真挚的情感。\n\n莫扎特的故事传遍了整个城市，人们纷纷来听这只音乐猫的演奏。它的音乐没有复杂的技巧，但却能够直达人心，让每个听众都感受到纯真的快乐和感动。\n\n莫扎特证明了，音乐是没有界限的语言，无论是人类还是动物，都能够通过音乐表达内心最真挚的情感。"
      },
      {
        id: 8,
        title: "艺术家小猫",
        emoji: "🎨",
        content: "在艺术区的一个小工作室里，住着一只充满创意的小猫，名叫小艺。它有着一身柔软的奶油色毛发，和一双充满智慧的绿色眼睛。小艺对色彩和形状有着独特的敏感性。\n\n小艺的主人是一位画家，工作室里到处都是画布、颜料和画笔。小艺从小就在这样的环境中长大，耳濡目染之下，它也对艺术产生了浓厚的兴趣。\n\n一开始，小艺只是好奇地观察主人作画。它会坐在画架旁边，专注地看着主人如何调配颜料，如何运用画笔。有时候，它还会用爪子轻轻地碰触画布，感受不同材质的纹理。\n\n一天，主人外出时忘记收拾颜料。小艺看到调色板上五彩斑斓的颜料，忍不住用爪子蘸了一些，然后在画布上留下了几个小小的爪印。\n\n当主人回来看到这幅'作品'时，不仅没有生气，反而被小艺的创意深深震撼。那些看似随意的爪印，在画布上形成了一种独特的抽象美感。\n\n从那以后，主人开始鼓励小艺进行艺术创作。他为小艺准备了专门的画布和无毒颜料。小艺的作品虽然抽象，但充满了生命力和原始的美感。\n\n小艺的画作在当地艺术展上展出，获得了观众的热烈好评。人们惊叹于这只小猫的艺术天赋，也被它纯真的创作热情所感动。小艺用自己的方式证明了，艺术没有固定的形式，创意和热情才是最重要的。"
      },
      {
        id: 9,
        title: "学者猫的图书馆",
        emoji: "📚",
        content: "在城市最古老的图书馆里，住着一只博学的猫咪，名叫牛顿。它有着一身深灰色的毛发，戴着一副小小的眼镜，看起来就像一位真正的学者。\n\n牛顿对知识有着无穷的渴望。它每天都会在图书馆的书架间穿梭，虽然不能真正阅读文字，但它能够感受到书籍中蕴含的智慧和力量。\n\n图书馆的管理员是一位慈祥的老奶奶，她很喜欢这只特别的猫咪。她发现牛顿总是能够帮助读者找到他们需要的书籍，就好像它真的理解每本书的内容一样。\n\n有一天，一个小男孩来到图书馆，他需要找一本关于天文学的书来完成作业，但是找了很久都没有找到。牛顿看到了小男孩的困扰，它轻巧地跳到书架上，用爪子指向了一本天文学书籍。\n\n小男孩惊讶地发现，那正是他需要的书。从那以后，牛顿成为了图书馆里最受欢迎的'助理管理员'。它帮助无数读者找到了他们需要的书籍，也见证了许多人在知识的海洋中成长。\n\n牛顿最喜欢的时光是在图书馆关门后，它会独自在书架间漫步，感受着书籍散发出的墨香。它相信，虽然自己不能阅读文字，但知识的力量已经深深地融入了它的生命中。\n\n牛顿用自己的方式诠释了学习的真谛：知识不仅存在于书本中，更存在于我们对世界的好奇心和探索精神中。"
      }
    ]
  },

  async onLaunch() {
    // 初始化主题
    const theme = wx.getStorageSync('theme') || 'light';
    this.globalData.theme = theme;

    // 初始化收藏
    const favorites = wx.getStorageSync('favorites') || [];
    this.globalData.favorites = favorites;

    // 初始化阅读记录
    const readStories = wx.getStorageSync('readStories') || [];
    this.globalData.readStories = readStories;

    // 初始化用户信息
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    this.globalData.userInfo = userInfo;
    this.globalData.isLoggedIn = !!token;

    // 初始化数据管理器
    await dataManager.init();

    // 如果已登录，尝试自动登录
    if (!this.globalData.isLoggedIn) {
      this.autoLogin();
    }
  },

  // 主题切换
  toggleTheme() {
    const newTheme = this.globalData.theme === 'light' ? 'dark' : 'light';
    this.globalData.theme = newTheme;
    wx.setStorageSync('theme', newTheme);
    
    // 通知所有页面更新主题
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.updateTheme) {
        page.updateTheme(newTheme);
      }
    });
  },

  // 自动登录
  async autoLogin() {
    try {
      wx.login({
        success: async (res) => {
          if (res.code) {
            const result = await userService.wxLogin(res.code);
            if (result.success) {
              this.globalData.userInfo = result.data.userInfo;
              this.globalData.isLoggedIn = true;

              // 触发登录成功事件
              this.triggerEvent('loginSuccess', result.data);
            }
          }
        }
      });
    } catch (error) {
      console.error('自动登录失败:', error);
    }
  },

  // 收藏管理（使用数据管理器）
  async toggleFavorite(storyId) {
    const favorites = this.globalData.favorites;
    const index = favorites.indexOf(storyId);

    if (index > -1) {
      // 取消收藏
      favorites.splice(index, 1);
      await dataManager.removeFavorite(storyId);
    } else {
      // 添加收藏
      favorites.push(storyId);
      await dataManager.addFavorite(storyId);
    }

    this.globalData.favorites = favorites;
    return favorites.includes(storyId);
  },

  isFavorite(storyId) {
    return this.globalData.favorites.includes(storyId);
  },

  // 获取故事
  getStoryById(id) {
    return this.globalData.stories.find(story => story.id === parseInt(id));
  },

  // 搜索故事
  searchStories(query) {
    if (!query) return this.globalData.stories;

    const lowerQuery = query.toLowerCase();
    return this.globalData.stories.filter(story =>
      story.title.toLowerCase().includes(lowerQuery) ||
      story.content.toLowerCase().includes(lowerQuery)
    );
  },

  // 标记故事为已读（使用数据管理器）
  async markStoryAsRead(storyId) {
    const readStories = this.globalData.readStories;
    if (!readStories.includes(storyId)) {
      readStories.push(storyId);
      this.globalData.readStories = readStories;
      await dataManager.recordReading(storyId);
    }
  },

  // 检查故事是否已读
  isStoryRead(storyId) {
    return this.globalData.readStories.includes(storyId);
  },

  // 获取阅读统计
  getReadingStats() {
    const totalStories = this.globalData.stories.length;
    const readCount = this.globalData.readStories.length;
    const favoriteCount = this.globalData.favorites.length;

    return {
      totalStories,
      readCount,
      favoriteCount,
      readProgress: totalStories > 0 ? Math.round((readCount / totalStories) * 100) : 0
    };
  },

  // 获取故事列表（API版本）
  async getStoriesFromAPI(params = {}) {
    return await dataManager.getStories(params);
  },

  // 获取故事详情（API版本）
  async getStoryDetailFromAPI(storyId) {
    return await dataManager.getStoryDetail(storyId);
  },

  // 搜索故事（API版本）
  async searchStoriesFromAPI(keyword, options = {}) {
    return await dataManager.searchStories(keyword, options);
  },

  // 触发自定义事件
  triggerEvent(eventName, data) {
    // 可以在这里实现全局事件系统
    console.log('Global Event:', eventName, data);
  },

  // 检查登录状态
  checkLoginStatus() {
    return this.globalData.isLoggedIn;
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo;
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    this.globalData.userInfo = userInfo;
    wx.setStorageSync('userInfo', userInfo);
  },

  // 退出登录
  async logout() {
    await userService.logout();
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;

    // 清除相关数据
    this.globalData.favorites = [];
    this.globalData.readStories = [];
  }
});
