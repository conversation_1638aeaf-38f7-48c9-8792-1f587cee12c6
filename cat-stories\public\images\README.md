# 猫咪故事插画目录

本目录用于存放Eric <PERSON>风格的猫咪故事插画。

## 文件命名规范

请按照以下命名规范保存生成的图片：

- `story-1-orange-cat-adventure.png` - 小橘猫的冒险
- `story-2-white-cat-moonlight.png` - 月光下的白猫
- `story-3-black-cat-garden.png` - 花园里的小黑猫
- `story-4-gray-cat-sleeping.png` - 爱睡觉的灰猫
- `story-5-tabby-cat-hero.png` - 勇敢的虎斑猫
- `story-6-calico-cat-fish.png` - 爱吃鱼的小花猫
- `story-7-music-cat-piano.png` - 会弹钢琴的音乐猫
- `story-8-art-cat-painting.png` - 爱画画的艺术猫
- `story-9-scholar-cat-books.png` - 爱读书的学者猫
- `magazine-cover.png` - 杂志封面

## 图片规格要求

- **格式**: PNG (推荐) 或 JPG
- **分辨率**: 最低 1024x1024 像素
- **宽高比**: 正方形 (1:1) 或 4:3
- **文件大小**: 建议小于 2MB
- **色彩模式**: RGB

## 生成指南

1. 使用 `/docs/ai-image-prompts.md` 中的提示词
2. 选择支持Eric Carle风格的AI图片生成工具
3. 生成后检查图片质量和风格一致性
4. 按照命名规范保存到此目录

## 当前状态

- [ ] story-1-orange-cat-adventure.png
- [ ] story-2-white-cat-moonlight.png
- [ ] story-3-black-cat-garden.png
- [ ] story-4-gray-cat-sleeping.png
- [ ] story-5-tabby-cat-hero.png
- [ ] story-6-calico-cat-fish.png
- [ ] story-7-music-cat-piano.png
- [ ] story-8-art-cat-painting.png
- [ ] story-9-scholar-cat-books.png
- [ ] magazine-cover.png

## 使用说明

图片会自动被 `ImageManager` 组件加载。如果图片不存在，会显示Eric Carle风格的占位符。

## 版权说明

所有图片应为原创或具有适当的使用许可。Eric Carle风格仅作为艺术参考，不侵犯原作者版权。
