'use strict';

var ApiLevel = /*#__PURE__*/ function(ApiLevel) {
    ApiLevel[ApiLevel["Trigger"] = 2] = "Trigger";
    ApiLevel[ApiLevel["Call"] = 1] = "Call";
    return ApiLevel;
}({});
function setLevelRef(instance, level) {
    instance.levelRefs[level] = {};
}
function getLevelRef(instance, level) {
    return instance.levelRefs[level];
}

exports.ApiLevel = ApiLevel;
exports.getLevelRef = getLevelRef;
exports.setLevelRef = setLevelRef;
