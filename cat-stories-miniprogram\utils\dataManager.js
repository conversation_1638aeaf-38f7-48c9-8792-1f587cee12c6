/**
 * 数据管理器 - 处理本地数据和远程数据的同步
 */
import storyService from '../services/storyService.js';
import userService from '../services/userService.js';

class DataManager {
  constructor() {
    this.isOnline = true;
    this.syncQueue = []; // 同步队列
    this.lastSyncTime = wx.getStorageSync('lastSyncTime') || 0;
  }

  /**
   * 初始化数据管理器
   */
  async init() {
    // 检查网络状态
    this.checkNetworkStatus();
    
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.isOnline = res.isConnected;
      if (this.isOnline) {
        this.processSyncQueue();
      }
    });

    // 如果在线，尝试同步数据
    if (this.isOnline) {
      await this.syncData();
    }
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.isOnline = res.networkType !== 'none';
      }
    });
  }

  /**
   * 获取故事列表（混合模式：优先远程，降级本地）
   */
  async getStories(params = {}) {
    if (this.isOnline) {
      try {
        const result = await storyService.getStories(params);
        if (result.success) {
          // 缓存到本地
          this.cacheStories(result.data);
          return result;
        }
      } catch (error) {
        console.warn('远程获取故事失败，使用本地缓存:', error);
      }
    }

    // 使用本地缓存
    return this.getLocalStories(params);
  }

  /**
   * 获取故事详情
   */
  async getStoryDetail(storyId) {
    if (this.isOnline) {
      try {
        const result = await storyService.getStoryDetail(storyId);
        if (result.success) {
          // 缓存到本地
          this.cacheStoryDetail(storyId, result.data);
          return result;
        }
      } catch (error) {
        console.warn('远程获取故事详情失败，使用本地缓存:', error);
      }
    }

    // 使用本地缓存
    return this.getLocalStoryDetail(storyId);
  }

  /**
   * 搜索故事
   */
  async searchStories(keyword, options = {}) {
    if (this.isOnline) {
      try {
        const result = await storyService.searchStories(keyword, options);
        if (result.success) {
          return result;
        }
      } catch (error) {
        console.warn('远程搜索失败，使用本地搜索:', error);
      }
    }

    // 使用本地搜索
    return this.searchLocalStories(keyword, options);
  }

  /**
   * 添加收藏
   */
  async addFavorite(storyId) {
    // 先更新本地
    const localFavorites = wx.getStorageSync('favorites') || [];
    if (!localFavorites.includes(storyId)) {
      localFavorites.push(storyId);
      wx.setStorageSync('favorites', localFavorites);
    }

    // 如果在线，同步到服务器
    if (this.isOnline) {
      try {
        const result = await userService.addFavorite(storyId);
        if (result.success) {
          return { success: true };
        }
      } catch (error) {
        // 添加到同步队列
        this.addToSyncQueue('addFavorite', { storyId });
      }
    } else {
      // 添加到同步队列
      this.addToSyncQueue('addFavorite', { storyId });
    }

    return { success: true };
  }

  /**
   * 取消收藏
   */
  async removeFavorite(storyId) {
    // 先更新本地
    const localFavorites = wx.getStorageSync('favorites') || [];
    const index = localFavorites.indexOf(storyId);
    if (index > -1) {
      localFavorites.splice(index, 1);
      wx.setStorageSync('favorites', localFavorites);
    }

    // 如果在线，同步到服务器
    if (this.isOnline) {
      try {
        const result = await userService.removeFavorite(storyId);
        if (result.success) {
          return { success: true };
        }
      } catch (error) {
        // 添加到同步队列
        this.addToSyncQueue('removeFavorite', { storyId });
      }
    } else {
      // 添加到同步队列
      this.addToSyncQueue('removeFavorite', { storyId });
    }

    return { success: true };
  }

  /**
   * 记录阅读
   */
  async recordReading(storyId) {
    // 先更新本地
    const readStories = wx.getStorageSync('readStories') || [];
    if (!readStories.includes(storyId)) {
      readStories.push(storyId);
      wx.setStorageSync('readStories', readStories);
    }

    // 如果在线，同步到服务器
    if (this.isOnline) {
      try {
        await storyService.recordReading(storyId);
      } catch (error) {
        // 添加到同步队列
        this.addToSyncQueue('recordReading', { storyId });
      }
    } else {
      // 添加到同步队列
      this.addToSyncQueue('recordReading', { storyId });
    }
  }

  /**
   * 缓存故事列表
   */
  cacheStories(stories) {
    const cacheData = {
      stories,
      timestamp: Date.now()
    };
    wx.setStorageSync('storiesCache', cacheData);
  }

  /**
   * 缓存故事详情
   */
  cacheStoryDetail(storyId, story) {
    const cacheKey = `story_${storyId}`;
    const cacheData = {
      story,
      timestamp: Date.now()
    };
    wx.setStorageSync(cacheKey, cacheData);
  }

  /**
   * 获取本地故事列表
   */
  getLocalStories(params = {}) {
    const cacheData = wx.getStorageSync('storiesCache');
    if (cacheData && cacheData.stories) {
      let stories = cacheData.stories;
      
      // 简单的分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      stories = stories.slice(start, end);
      
      return {
        success: true,
        data: stories,
        total: cacheData.stories.length,
        fromCache: true
      };
    }

    // 返回默认数据
    return this.getDefaultStories();
  }

  /**
   * 获取本地故事详情
   */
  getLocalStoryDetail(storyId) {
    const cacheKey = `story_${storyId}`;
    const cacheData = wx.getStorageSync(cacheKey);
    
    if (cacheData && cacheData.story) {
      return {
        success: true,
        data: cacheData.story,
        fromCache: true
      };
    }

    // 从默认数据中查找
    const app = getApp();
    const story = app.getStoryById(storyId);
    
    if (story) {
      return {
        success: true,
        data: story,
        fromCache: true
      };
    }

    return {
      success: false,
      error: '故事不存在',
      data: null
    };
  }

  /**
   * 本地搜索故事
   */
  searchLocalStories(keyword, options = {}) {
    const app = getApp();
    const stories = app.searchStories(keyword);
    
    return {
      success: true,
      data: stories,
      total: stories.length,
      fromCache: true
    };
  }

  /**
   * 获取默认故事数据
   */
  getDefaultStories() {
    const app = getApp();
    return {
      success: true,
      data: app.globalData.stories,
      total: app.globalData.stories.length,
      fromCache: true
    };
  }

  /**
   * 添加到同步队列
   */
  addToSyncQueue(action, data) {
    this.syncQueue.push({
      action,
      data,
      timestamp: Date.now()
    });
    
    // 保存到本地
    wx.setStorageSync('syncQueue', this.syncQueue);
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.syncQueue.length === 0) return;

    const queue = [...this.syncQueue];
    this.syncQueue = [];

    for (const item of queue) {
      try {
        await this.processSyncItem(item);
      } catch (error) {
        console.error('同步失败:', item, error);
        // 重新加入队列
        this.syncQueue.push(item);
      }
    }

    // 更新本地队列
    wx.setStorageSync('syncQueue', this.syncQueue);
  }

  /**
   * 处理单个同步项
   */
  async processSyncItem(item) {
    const { action, data } = item;

    switch (action) {
      case 'addFavorite':
        await userService.addFavorite(data.storyId);
        break;
      case 'removeFavorite':
        await userService.removeFavorite(data.storyId);
        break;
      case 'recordReading':
        await storyService.recordReading(data.storyId);
        break;
      default:
        console.warn('未知的同步操作:', action);
    }
  }

  /**
   * 同步数据
   */
  async syncData() {
    try {
      // 加载本地同步队列
      const localQueue = wx.getStorageSync('syncQueue') || [];
      this.syncQueue = localQueue;

      // 处理同步队列
      await this.processSyncQueue();

      // 同步本地数据到服务器
      const localData = {
        favorites: wx.getStorageSync('favorites') || [],
        readStories: wx.getStorageSync('readStories') || [],
        searchHistory: wx.getStorageSync('searchHistory') || [],
        theme: wx.getStorageSync('theme') || 'light'
      };

      await userService.syncLocalData(localData);

      // 更新同步时间
      this.lastSyncTime = Date.now();
      wx.setStorageSync('lastSyncTime', this.lastSyncTime);

    } catch (error) {
      console.error('数据同步失败:', error);
    }
  }
}

// 创建数据管理器实例
const dataManager = new DataManager();

export default dataManager;
