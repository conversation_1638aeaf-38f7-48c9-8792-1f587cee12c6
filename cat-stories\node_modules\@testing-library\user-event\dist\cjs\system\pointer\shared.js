'use strict';

function isDifferentPointerPosition(positionA, positionB) {
    var _positionA_coords, _positionB_coords, _positionA_coords1, _positionB_coords1, _positionA_coords2, _positionB_coords2, _positionA_coords3, _positionB_coords3, _positionA_coords4, _positionB_coords4, _positionA_coords5, _positionB_coords5, _positionA_coords6, _positionB_coords6, _positionA_coords7, _positionB_coords7, _positionA_coords8, _positionB_coords8, _positionA_coords9, _positionB_coords9, _positionA_caret, _positionB_caret, _positionA_caret1, _positionB_caret1;
    return positionA.target !== positionB.target || ((_positionA_coords = positionA.coords) === null || _positionA_coords === undefined ? undefined : _positionA_coords.x) !== ((_positionB_coords = positionB.coords) === null || _positionB_coords === undefined ? undefined : _positionB_coords.x) || ((_positionA_coords1 = positionA.coords) === null || _positionA_coords1 === undefined ? undefined : _positionA_coords1.y) !== ((_positionB_coords1 = positionB.coords) === null || _positionB_coords1 === undefined ? undefined : _positionB_coords1.y) || ((_positionA_coords2 = positionA.coords) === null || _positionA_coords2 === undefined ? undefined : _positionA_coords2.clientX) !== ((_positionB_coords2 = positionB.coords) === null || _positionB_coords2 === undefined ? undefined : _positionB_coords2.clientX) || ((_positionA_coords3 = positionA.coords) === null || _positionA_coords3 === undefined ? undefined : _positionA_coords3.clientY) !== ((_positionB_coords3 = positionB.coords) === null || _positionB_coords3 === undefined ? undefined : _positionB_coords3.clientY) || ((_positionA_coords4 = positionA.coords) === null || _positionA_coords4 === undefined ? undefined : _positionA_coords4.offsetX) !== ((_positionB_coords4 = positionB.coords) === null || _positionB_coords4 === undefined ? undefined : _positionB_coords4.offsetX) || ((_positionA_coords5 = positionA.coords) === null || _positionA_coords5 === undefined ? undefined : _positionA_coords5.offsetY) !== ((_positionB_coords5 = positionB.coords) === null || _positionB_coords5 === undefined ? undefined : _positionB_coords5.offsetY) || ((_positionA_coords6 = positionA.coords) === null || _positionA_coords6 === undefined ? undefined : _positionA_coords6.pageX) !== ((_positionB_coords6 = positionB.coords) === null || _positionB_coords6 === undefined ? undefined : _positionB_coords6.pageX) || ((_positionA_coords7 = positionA.coords) === null || _positionA_coords7 === undefined ? undefined : _positionA_coords7.pageY) !== ((_positionB_coords7 = positionB.coords) === null || _positionB_coords7 === undefined ? undefined : _positionB_coords7.pageY) || ((_positionA_coords8 = positionA.coords) === null || _positionA_coords8 === undefined ? undefined : _positionA_coords8.screenX) !== ((_positionB_coords8 = positionB.coords) === null || _positionB_coords8 === undefined ? undefined : _positionB_coords8.screenX) || ((_positionA_coords9 = positionA.coords) === null || _positionA_coords9 === undefined ? undefined : _positionA_coords9.screenY) !== ((_positionB_coords9 = positionB.coords) === null || _positionB_coords9 === undefined ? undefined : _positionB_coords9.screenY) || ((_positionA_caret = positionA.caret) === null || _positionA_caret === undefined ? undefined : _positionA_caret.node) !== ((_positionB_caret = positionB.caret) === null || _positionB_caret === undefined ? undefined : _positionB_caret.node) || ((_positionA_caret1 = positionA.caret) === null || _positionA_caret1 === undefined ? undefined : _positionA_caret1.offset) !== ((_positionB_caret1 = positionB.caret) === null || _positionB_caret1 === undefined ? undefined : _positionB_caret1.offset);
}

exports.isDifferentPointerPosition = isDifferentPointerPosition;
