/* pages/story-detail/story-detail.wxss */

/* 返回按钮 */
.back-button {
  position: fixed;
  top: 32rpx;
  left: 32rpx;
  padding: 16rpx 32rpx;
  border: 2rpx solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 28rpx;
  font-weight: bold;
  z-index: 99;
  border-radius: 8rpx;
}

/* 故事详情 */
.story-detail {
  padding-top: 32rpx;
}

.story-header {
  margin-bottom: 64rpx;
}

.story-title {
  font-size: 72rpx;
  font-weight: 800;
  line-height: 1.3;
  margin-bottom: 48rpx;
  color: var(--text-color);
  text-align: center;
  letter-spacing: 1rpx;
  position: relative;
  padding: 0 32rpx;
}

.story-title::after {
  content: '';
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 6rpx;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--warm-orange) 100%);
  border-radius: 3rpx;
}

.story-image-large {
  height: 600rpx;
  margin-bottom: 32rpx;
}

.story-emoji-large {
  font-size: 240rpx;
  line-height: 1;
}

/* 故事内容 */
.story-content {
  margin-bottom: 64rpx;
}

.story-text {
  font-size: 32rpx;
  line-height: 1.8;
  color: var(--text-color);
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 操作按钮 */
.story-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 64rpx;
}

.favorite-btn.large {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 故事导航 */
.story-navigation {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 64rpx;
}

.nav-btn {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border: 4rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  text-align: left;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: var(--hover-bg);
  transform: translateX(8rpx);
}

.nav-btn.prev {
  justify-content: flex-start;
}

.nav-btn.next {
  justify-content: flex-end;
}

.nav-emoji {
  font-size: 48rpx;
  margin: 0 24rpx;
}

.nav-text {
  flex: 1;
}

.nav-label {
  font-size: 24rpx;
  color: var(--secondary-text);
  text-transform: uppercase;
  letter-spacing: 1rpx;
  margin-bottom: 8rpx;
}

.nav-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 128rpx 32rpx;
}

.loading-emoji {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  animation: bounce 1s infinite;
}

.loading-text {
  font-size: 32rpx;
  color: var(--secondary-text);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

/* 响应式 */
@media (min-width: 768rpx) {
  .story-title {
    font-size: 96rpx;
  }
  
  .story-image-large {
    height: 800rpx;
  }
  
  .story-emoji-large {
    font-size: 320rpx;
  }
  
  .story-text {
    font-size: 36rpx;
    line-height: 2;
  }
  
  .story-actions {
    flex-direction: row;
    justify-content: center;
  }
  
  .story-actions .btn,
  .story-actions .favorite-btn {
    flex: 1;
    max-width: 300rpx;
  }
  
  .story-navigation {
    flex-direction: row;
  }
  
  .nav-btn {
    flex: 1;
  }
}
