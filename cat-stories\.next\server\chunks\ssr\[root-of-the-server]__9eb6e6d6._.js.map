{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/StoryCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\ninterface StoryCardProps {\n  story: CatStory;\n  onClick: () => void;\n  isFavorite?: boolean;\n  onToggleFavorite?: () => void;\n}\n\nexport default function StoryCard({ story, onClick, isFavorite = false, onToggleFavorite }: StoryCardProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <div\n      className={`${story.color} rounded-2xl p-6 cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl relative overflow-hidden`}\n      onClick={onClick}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      {/* Favorite button */}\n      {onToggleFavorite && (\n        <button\n          onClick={(e) => {\n            e.stopPropagation();\n            onToggleFavorite();\n          }}\n          className=\"absolute top-3 right-3 z-20 p-2 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 transition-all duration-200 hover:scale-110\"\n          aria-label={isFavorite ? \"取消收藏\" : \"添加收藏\"}\n        >\n          <span className={`text-xl ${isFavorite ? 'text-red-500' : 'text-gray-400'}`}>\n            {isFavorite ? '❤️' : '🤍'}\n          </span>\n        </button>\n      )}\n\n      {/* Decorative elements */}\n      <div className=\"absolute top-2 left-2 opacity-20\">\n        <div className=\"text-2xl\">🐾</div>\n      </div>\n      <div className=\"absolute bottom-2 left-2 opacity-20\">\n        <div className=\"text-xl\">✨</div>\n      </div>\n      \n      <div className=\"text-center relative z-10\">\n        <div className={`text-6xl mb-4 transition-transform duration-300 ${isHovered ? 'scale-110 wiggle' : ''}`}>\n          {story.emoji}\n        </div>\n        <h3 className=\"text-xl font-bold text-gray-800 mb-3\">\n          {story.title}\n        </h3>\n        <p className=\"text-gray-700 text-sm line-clamp-3 mb-4\">\n          {story.content.substring(0, 100)}...\n        </p>\n        <button \n          className={`bg-white bg-opacity-70 hover:bg-opacity-100 px-4 py-2 rounded-full text-sm font-medium text-gray-800 transition-all duration-200 ${isHovered ? 'bounce-gentle' : ''}`}\n        >\n          阅读故事 📖\n        </button>\n      </div>\n      \n      {/* Hover effect overlay */}\n      {isHovered && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-10 rounded-2xl pointer-events-none\" />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBe,SAAS,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,KAAK,EAAE,gBAAgB,EAAkB;IACxG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QACC,WAAW,GAAG,MAAM,KAAK,CAAC,8HAA8H,CAAC;QACzJ,SAAS;QACT,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;YAGhC,kCACC,8OAAC;gBACC,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB;gBACF;gBACA,WAAU;gBACV,cAAY,aAAa,SAAS;0BAElC,cAAA,8OAAC;oBAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,iBAAiB,iBAAiB;8BACxE,aAAa,OAAO;;;;;;;;;;;0BAM3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAW;;;;;;;;;;;0BAE5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAU;;;;;;;;;;;0BAG3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,gDAAgD,EAAE,YAAY,qBAAqB,IAAI;kCACrG,MAAM,KAAK;;;;;;kCAEd,8OAAC;wBAAG,WAAU;kCACX,MAAM,KAAK;;;;;;kCAEd,8OAAC;wBAAE,WAAU;;4BACV,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;4BAAK;;;;;;;kCAEnC,8OAAC;wBACC,WAAW,CAAC,iIAAiI,EAAE,YAAY,kBAAkB,IAAI;kCAClL;;;;;;;;;;;;YAMF,2BACC,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/StoryModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\ninterface StoryModalProps {\n  story: CatStory | null;\n  onClose: () => void;\n}\n\nexport default function StoryModal({ story, onClose }: StoryModalProps) {\n  useEffect(() => {\n    if (story) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [story]);\n\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (story) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [story, onClose]);\n\n  if (!story) return null;\n\n  return (\n    <div \n      className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200\"\n      onClick={onClose}\n    >\n      <div \n        className=\"bg-white rounded-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto animate-in zoom-in-95 duration-200\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        <div className={`${story.color} p-6 rounded-t-2xl relative`}>\n          {/* Decorative elements */}\n          <div className=\"absolute top-4 right-16 opacity-30 text-2xl\">🌟</div>\n          <div className=\"absolute top-8 right-8 opacity-30 text-xl\">💫</div>\n          \n          <div className=\"flex justify-between items-center relative z-10\">\n            <h2 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n              <span className=\"text-4xl animate-pulse\">{story.emoji}</span>\n              <span>{story.title}</span>\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-600 hover:text-gray-800 text-3xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-50 transition-all duration-200\"\n              aria-label=\"关闭故事\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"p-6\">\n          <div className=\"prose prose-lg max-w-none\">\n            <p className=\"text-gray-700 leading-relaxed text-lg first-letter:text-4xl first-letter:font-bold first-letter:text-purple-600 first-letter:float-left first-letter:mr-2 first-letter:mt-1\">\n              {story.content}\n            </p>\n          </div>\n          \n          {/* Story end decoration */}\n          <div className=\"text-center mt-8 pt-6 border-t border-gray-200\">\n            <div className=\"text-2xl mb-2\">🐾 故事结束 🐾</div>\n            <p className=\"text-gray-500 text-sm\">希望你喜欢这个温馨的猫咪故事</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBe,SAAS,WAAW,EAAE,KAAK,EAAE,OAAO,EAAmB;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,OAAO;YACT,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,8OAAC;YACC,WAAU;YACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8BAEjC,8OAAC;oBAAI,WAAW,GAAG,MAAM,KAAK,CAAC,2BAA2B,CAAC;;sCAEzD,8OAAC;4BAAI,WAAU;sCAA8C;;;;;;sCAC7D,8OAAC;4BAAI,WAAU;sCAA4C;;;;;;sCAE3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;sDACrD,8OAAC;sDAAM,MAAM,KAAK;;;;;;;;;;;;8CAEpB,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CACZ;;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,MAAM,OAAO;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/CatIcon.tsx"], "sourcesContent": ["interface CatIconProps {\n  className?: string;\n  size?: number;\n}\n\nexport default function CatIcon({ className = \"\", size = 24 }: CatIconProps) {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      {/* Cat face */}\n      <circle cx=\"12\" cy=\"14\" r=\"8\" fill=\"#FFA500\" stroke=\"#FF8C00\" strokeWidth=\"1\"/>\n      \n      {/* Cat ears */}\n      <path d=\"M6 8 L8 4 L10 8 Z\" fill=\"#FFA500\" stroke=\"#FF8C00\" strokeWidth=\"1\"/>\n      <path d=\"M14 8 L16 4 L18 8 Z\" fill=\"#FFA500\" stroke=\"#FF8C00\" strokeWidth=\"1\"/>\n      \n      {/* Inner ears */}\n      <path d=\"M7 7 L8 5 L9 7 Z\" fill=\"#FFB84D\"/>\n      <path d=\"M15 7 L16 5 L17 7 Z\" fill=\"#FFB84D\"/>\n      \n      {/* Eyes */}\n      <circle cx=\"9\" cy=\"12\" r=\"1.5\" fill=\"#000\"/>\n      <circle cx=\"15\" cy=\"12\" r=\"1.5\" fill=\"#000\"/>\n      <circle cx=\"9.5\" cy=\"11.5\" r=\"0.5\" fill=\"#FFF\"/>\n      <circle cx=\"15.5\" cy=\"11.5\" r=\"0.5\" fill=\"#FFF\"/>\n      \n      {/* Nose */}\n      <path d=\"M12 14 L11 15 L13 15 Z\" fill=\"#FF69B4\"/>\n      \n      {/* Mouth */}\n      <path d=\"M12 15 Q10 17 8 16\" stroke=\"#000\" strokeWidth=\"1\" fill=\"none\"/>\n      <path d=\"M12 15 Q14 17 16 16\" stroke=\"#000\" strokeWidth=\"1\" fill=\"none\"/>\n      \n      {/* Whiskers */}\n      <line x1=\"5\" y1=\"13\" x2=\"8\" y2=\"13\" stroke=\"#000\" strokeWidth=\"1\"/>\n      <line x1=\"5\" y1=\"15\" x2=\"8\" y2=\"14\" stroke=\"#000\" strokeWidth=\"1\"/>\n      <line x1=\"16\" y1=\"13\" x2=\"19\" y2=\"13\" stroke=\"#000\" strokeWidth=\"1\"/>\n      <line x1=\"16\" y1=\"14\" x2=\"19\" y2=\"15\" stroke=\"#000\" strokeWidth=\"1\"/>\n    </svg>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAKe,SAAS,QAAQ,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAgB;IACzE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAGX,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;gBAAU,QAAO;gBAAU,aAAY;;;;;;0BAG1E,8OAAC;gBAAK,GAAE;gBAAoB,MAAK;gBAAU,QAAO;gBAAU,aAAY;;;;;;0BACxE,8OAAC;gBAAK,GAAE;gBAAsB,MAAK;gBAAU,QAAO;gBAAU,aAAY;;;;;;0BAG1E,8OAAC;gBAAK,GAAE;gBAAmB,MAAK;;;;;;0BAChC,8OAAC;gBAAK,GAAE;gBAAsB,MAAK;;;;;;0BAGnC,8OAAC;gBAAO,IAAG;gBAAI,IAAG;gBAAK,GAAE;gBAAM,MAAK;;;;;;0BACpC,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAM,MAAK;;;;;;0BACrC,8OAAC;gBAAO,IAAG;gBAAM,IAAG;gBAAO,GAAE;gBAAM,MAAK;;;;;;0BACxC,8OAAC;gBAAO,IAAG;gBAAO,IAAG;gBAAO,GAAE;gBAAM,MAAK;;;;;;0BAGzC,8OAAC;gBAAK,GAAE;gBAAyB,MAAK;;;;;;0BAGtC,8OAAC;gBAAK,GAAE;gBAAqB,QAAO;gBAAO,aAAY;gBAAI,MAAK;;;;;;0BAChE,8OAAC;gBAAK,GAAE;gBAAsB,QAAO;gBAAO,aAAY;gBAAI,MAAK;;;;;;0BAGjE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAO,aAAY;;;;;;0BAC9D,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAO,aAAY;;;;;;0BAC9D,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAO,aAAY;;;;;;0BAChE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAO,aAAY;;;;;;;;;;;;AAGtE", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void;\n  placeholder?: string;\n}\n\nexport default function SearchBar({ onSearch, placeholder = \"搜索猫咪故事...\" }: SearchBarProps) {\n  const [query, setQuery] = useState('');\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSearch(query);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setQuery(value);\n    onSearch(value); // Real-time search\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"w-full max-w-md mx-auto mb-8\">\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={query}\n          onChange={handleChange}\n          placeholder={placeholder}\n          className=\"w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border-2 border-pink-200 rounded-full focus:outline-none focus:border-pink-400 focus:ring-2 focus:ring-pink-200 transition-all duration-200\"\n        />\n        <div className=\"absolute left-4 top-1/2 transform -translate-y-1/2\">\n          <svg\n            className=\"w-5 h-5 text-pink-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            />\n          </svg>\n        </div>\n        {query && (\n          <button\n            type=\"button\"\n            onClick={() => {\n              setQuery('');\n              onSearch('');\n            }}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        )}\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,WAAW,EAAkB;IACvF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,SAAS,QAAQ,mBAAmB;IACtC;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;kBACtC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,MAAK;oBACL,OAAO;oBACP,UAAU;oBACV,aAAa;oBACb,WAAU;;;;;;8BAEZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;;;;;gBAIP,uBACC,8OAAC;oBACC,MAAK;oBACL,SAAS;wBACP,SAAS;wBACT,SAAS;oBACX;oBACA,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/hooks/useFavorites.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useFavorites() {\n  const [favorites, setFavorites] = useState<number[]>([]);\n\n  // Load favorites from localStorage on mount\n  useEffect(() => {\n    const saved = localStorage.getItem('cat-stories-favorites');\n    if (saved) {\n      try {\n        setFavorites(JSON.parse(saved));\n      } catch (error) {\n        console.error('Error loading favorites:', error);\n      }\n    }\n  }, []);\n\n  // Save favorites to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cat-stories-favorites', JSON.stringify(favorites));\n  }, [favorites]);\n\n  const addToFavorites = (storyId: number) => {\n    setFavorites(prev => [...prev, storyId]);\n  };\n\n  const removeFromFavorites = (storyId: number) => {\n    setFavorites(prev => prev.filter(id => id !== storyId));\n  };\n\n  const toggleFavorite = (storyId: number) => {\n    if (favorites.includes(storyId)) {\n      removeFromFavorites(storyId);\n    } else {\n      addToFavorites(storyId);\n    }\n  };\n\n  const isFavorite = (storyId: number) => {\n    return favorites.includes(storyId);\n  };\n\n  return {\n    favorites,\n    addToFavorites,\n    removeFromFavorites,\n    toggleFavorite,\n    isFavorite\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,IAAI;gBACF,aAAa,KAAK,KAAK,CAAC;YAC1B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;IACF,GAAG,EAAE;IAEL,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;IAC/D,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IACzC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,QAAQ,CAAC,UAAU;YAC/B,oBAAoB;QACtB,OAAO;YACL,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,UAAU,QAAQ,CAAC;IAC5B;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport StoryCard from '@/components/StoryCard';\nimport StoryModal from '@/components/StoryModal';\nimport CatIcon from '@/components/CatIcon';\nimport SearchBar from '@/components/SearchBar';\nimport { useFavorites } from '@/hooks/useFavorites';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\nconst catStories: CatStory[] = [\n  {\n    id: 1,\n    title: \"小橘猫的冒险\",\n    content: \"从前有一只可爱的小橘猫叫做咪咪，它住在一个温馨的小屋里。每天早上，咪咪都会在阳光下伸懒腰，然后开始它的日常冒险。今天，咪咪发现了花园里有一只美丽的蝴蝶，它决定跟着蝴蝶去探索未知的世界...\",\n    emoji: \"🐱\",\n    color: \"bg-gradient-to-br from-orange-200 to-orange-300\"\n  },\n  {\n    id: 2,\n    title: \"月光下的白猫\",\n    content: \"在一个宁静的夜晚，一只雪白的猫咪悄悄地走在月光下。它的毛发在月光的照耀下闪闪发光，就像天使一样美丽。白猫喜欢在夜晚漫步，因为这时候世界特别安静，它可以听到自己内心的声音...\",\n    emoji: \"🌙\",\n    color: \"bg-gradient-to-br from-blue-200 to-purple-300\"\n  },\n  {\n    id: 3,\n    title: \"花园里的小黑猫\",\n    content: \"小黑猫露娜最喜欢在花园里玩耍。它会在玫瑰花丛中捉迷藏，在薰衣草田里打滚，还会和蜜蜂做朋友。虽然其他动物有时候会害怕它的黑色毛发，但露娜其实是最温柔善良的猫咪...\",\n    emoji: \"🌸\",\n    color: \"bg-gradient-to-br from-pink-200 to-rose-300\"\n  },\n  {\n    id: 4,\n    title: \"爱睡觉的灰猫\",\n    content: \"灰猫咕咕是世界上最爱睡觉的猫咪。它可以在任何地方睡着：阳光下的窗台、柔软的沙发、甚至是主人的键盘上。咕咕的梦境总是充满了鱼和毛线球，每次醒来都会满足地打个哈欠...\",\n    emoji: \"😴\",\n    color: \"bg-gradient-to-br from-gray-200 to-slate-300\"\n  },\n  {\n    id: 5,\n    title: \"勇敢的虎斑猫\",\n    content: \"虎斑猫托尼是社区里最勇敢的猫咪。当其他小动物遇到困难时，托尼总是第一个站出来帮助。它曾经救过掉进水池的小鸟，也帮助过迷路的小老鼠找到回家的路。大家都很敬佩托尼的勇气和善良...\",\n    emoji: \"🦸\",\n    color: \"bg-gradient-to-br from-yellow-200 to-amber-300\"\n  },\n  {\n    id: 6,\n    title: \"爱吃鱼的小花猫\",\n    content: \"小花猫咪咪最大的爱好就是吃鱼。它知道镇上每一家鱼店的开门时间，也认识所有卖鱼的叔叔阿姨。咪咪特别聪明，它学会了用可爱的眼神和甜美的叫声来获得美味的鱼儿。每天的鱼餐时间是咪咪最快乐的时光...\",\n    emoji: \"🐟\",\n    color: \"bg-gradient-to-br from-teal-200 to-cyan-300\"\n  },\n  {\n    id: 7,\n    title: \"会弹钢琴的音乐猫\",\n    content: \"在一个音乐世家里住着一只特别的猫咪叫做莫扎特。它从小就对音乐有着天生的敏感，经常在主人练琴时静静地坐在旁边聆听。有一天，莫扎特竟然学会了用爪子弹奏简单的旋律，成为了世界上第一只会弹钢琴的猫咪...\",\n    emoji: \"🎹\",\n    color: \"bg-gradient-to-br from-indigo-200 to-blue-300\"\n  },\n  {\n    id: 8,\n    title: \"爱画画的艺术猫\",\n    content: \"小艺是一只充满创意的猫咪，它最喜欢用爪子蘸颜料在画布上创作。它的作品色彩斑斓，充满了猫咪独特的视角和想象力。小艺的画作在当地艺术展上大受欢迎，人们都惊叹于这只猫咪的艺术天赋...\",\n    emoji: \"🎨\",\n    color: \"bg-gradient-to-br from-red-200 to-pink-300\"\n  },\n  {\n    id: 9,\n    title: \"爱读书的学者猫\",\n    content: \"图书馆里住着一只博学的猫咪叫做牛顿。它每天都会坐在书架旁，似乎在认真地'阅读'各种书籍。虽然猫咪不会真的读书，但牛顿对知识的渴望和对学习的热爱感染了每一个来图书馆的人...\",\n    emoji: \"📚\",\n    color: \"bg-gradient-to-br from-green-200 to-emerald-300\"\n  }\n];\n\nexport default function Home() {\n  const [selectedStory, setSelectedStory] = useState<CatStory | null>(null);\n  const [currentPage, setCurrentPage] = useState(0);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);\n  const { toggleFavorite, isFavorite, favorites } = useFavorites();\n  const storiesPerPage = 3;\n\n  // Filter stories based on search query and favorites\n  const filteredStories = useMemo(() => {\n    let stories = catStories;\n\n    // Filter by favorites if enabled\n    if (showFavoritesOnly) {\n      stories = stories.filter(story => favorites.includes(story.id));\n    }\n\n    // Filter by search query\n    if (searchQuery.trim()) {\n      stories = stories.filter(story =>\n        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        story.content.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    return stories;\n  }, [searchQuery, showFavoritesOnly, favorites]);\n\n  const totalPages = Math.ceil(filteredStories.length / storiesPerPage);\n  const currentStories = filteredStories.slice(\n    currentPage * storiesPerPage,\n    (currentPage + 1) * storiesPerPage\n  );\n\n  // Reset to first page when search changes\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    setCurrentPage(0);\n  };\n\n  // Toggle favorites filter\n  const handleToggleFavoritesFilter = () => {\n    setShowFavoritesOnly(!showFavoritesOnly);\n    setCurrentPage(0);\n  };\n\n  // Random story function\n  const handleRandomStory = () => {\n    const randomIndex = Math.floor(Math.random() * catStories.length);\n    setSelectedStory(catStories[randomIndex]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50\">\n      {/* Header */}\n      <header className=\"text-center py-8 px-4\">\n        <div className=\"flex items-center justify-center gap-4 mb-4\">\n          <CatIcon size={48} className=\"bounce-gentle\" />\n          <h1 className=\"text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600\">\n            可爱猫咪故事集\n          </h1>\n          <CatIcon size={48} className=\"bounce-gentle\" />\n        </div>\n        <p className=\"text-lg text-gray-600 max-w-2xl mx-auto mb-6\">\n          欢迎来到温馨的猫咪世界，这里有最可爱的猫咪故事等着你来发现！\n        </p>\n\n        {/* Action Buttons */}\n        <div className=\"flex gap-4 justify-center\">\n          <button\n            onClick={handleRandomStory}\n            className=\"bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-3 rounded-full font-medium hover:from-pink-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl\"\n          >\n            🎲 随机故事\n          </button>\n\n          <button\n            onClick={handleToggleFavoritesFilter}\n            className={`px-6 py-3 rounded-full font-medium transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl ${\n              showFavoritesOnly\n                ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white hover:from-red-600 hover:to-pink-700'\n                : 'bg-white text-gray-700 hover:bg-gray-50 border-2 border-pink-200'\n            }`}\n          >\n            {showFavoritesOnly ? '❤️ 显示全部' : '🤍 只看收藏'} ({favorites.length})\n          </button>\n        </div>\n      </header>\n\n      {/* Story Modal */}\n      <StoryModal\n        story={selectedStory}\n        onClose={() => setSelectedStory(null)}\n      />\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 pb-8\">\n        {/* Search Bar */}\n        <SearchBar onSearch={handleSearch} />\n\n        {/* Search Results Info */}\n        {searchQuery && (\n          <div className=\"text-center mb-6\">\n            <p className=\"text-gray-600\">\n              找到 <span className=\"font-bold text-pink-600\">{filteredStories.length}</span> 个关于\n              \"<span className=\"font-bold text-purple-600\">{searchQuery}</span>\" 的故事\n            </p>\n          </div>\n        )}\n\n        {/* Story Grid */}\n        {currentStories.length > 0 ? (\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n            {currentStories.map((story) => (\n              <StoryCard\n                key={story.id}\n                story={story}\n                onClick={() => setSelectedStory(story)}\n                isFavorite={isFavorite(story.id)}\n                onToggleFavorite={() => toggleFavorite(story.id)}\n              />\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">😿</div>\n            <h3 className=\"text-xl font-bold text-gray-600 mb-2\">没有找到相关故事</h3>\n            <p className=\"text-gray-500\">试试其他关键词吧！</p>\n          </div>\n        )}\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"flex justify-center items-center gap-4\">\n            <button\n              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n              disabled={currentPage === 0}\n              className=\"px-4 py-2 rounded-full bg-white shadow-md disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg transition-all duration-200\"\n            >\n              ← 上一页\n            </button>\n            <span className=\"text-gray-600\">\n              第 {currentPage + 1} 页，共 {totalPages} 页\n            </span>\n            <button\n              onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}\n              disabled={currentPage === totalPages - 1}\n              className=\"px-4 py-2 rounded-full bg-white shadow-md disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg transition-all duration-200\"\n            >\n              下一页 →\n            </button>\n          </div>\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"text-center py-8 px-4 bg-white bg-opacity-50\">\n        <p className=\"text-gray-600 mb-2\">\n          🐱 用爱心制作的猫咪故事网站 🐱\n        </p>\n        <p className=\"text-sm text-gray-500\">\n          每一个故事都充满了温暖和快乐\n        </p>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAiBA,MAAM,aAAyB;IAC7B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC7D,MAAM,iBAAiB;IAEvB,qDAAqD;IACrD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,UAAU;QAEd,iCAAiC;QACjC,IAAI,mBAAmB;YACrB,UAAU,QAAQ,MAAM,CAAC,CAAA,QAAS,UAAU,QAAQ,CAAC,MAAM,EAAE;QAC/D;QAEA,yBAAyB;QACzB,IAAI,YAAY,IAAI,IAAI;YACtB,UAAU,QAAQ,MAAM,CAAC,CAAA,QACvB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEhE;QAEA,OAAO;IACT,GAAG;QAAC;QAAa;QAAmB;KAAU;IAE9C,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG;IACtD,MAAM,iBAAiB,gBAAgB,KAAK,CAC1C,cAAc,gBACd,CAAC,cAAc,CAAC,IAAI;IAGtB,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe;IACjB;IAEA,0BAA0B;IAC1B,MAAM,8BAA8B;QAClC,qBAAqB,CAAC;QACtB,eAAe;IACjB;IAEA,wBAAwB;IACxB,MAAM,oBAAoB;QACxB,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;QAChE,iBAAiB,UAAU,CAAC,YAAY;IAC1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC7B,8OAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,8OAAC,6HAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;kCAE/B,8OAAC;wBAAE,WAAU;kCAA+C;;;;;;kCAK5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,mHAAmH,EAC7H,oBACI,8FACA,oEACJ;;oCAED,oBAAoB,YAAY;oCAAU;oCAAG,UAAU,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;0BAMrE,8OAAC,gIAAA,CAAA,UAAU;gBACT,OAAO;gBACP,SAAS,IAAM,iBAAiB;;;;;;0BAIlC,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,+HAAA,CAAA,UAAS;wBAAC,UAAU;;;;;;oBAGpB,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAgB;8CACxB,8OAAC;oCAAK,WAAU;8CAA2B,gBAAgB,MAAM;;;;;;gCAAQ;8CAC3E,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;gCAAmB;;;;;;;;;;;;oBAMtE,eAAe,MAAM,GAAG,kBACvB,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,+HAAA,CAAA,UAAS;gCAER,OAAO;gCACP,SAAS,IAAM,iBAAiB;gCAChC,YAAY,WAAW,MAAM,EAAE;gCAC/B,kBAAkB,IAAM,eAAe,MAAM,EAAE;+BAJ1C,MAAM,EAAE;;;;;;;;;6CASnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAKhC,aAAa,mBACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gCACxD,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAK,WAAU;;oCAAgB;oCAC3B,cAAc;oCAAE;oCAAM;oCAAW;;;;;;;0CAEtC,8OAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;gCACrE,UAAU,gBAAgB,aAAa;gCACvC,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}