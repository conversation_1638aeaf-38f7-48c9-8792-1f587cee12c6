/* pages/table-of-contents/table-of-contents.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 64rpx;
}

.page-title {
  font-size: 72rpx;
  font-weight: 900;
  margin-bottom: 16rpx;
  color: var(--text-color);
  text-transform: uppercase;
  letter-spacing: 4rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: var(--secondary-text);
  letter-spacing: 2rpx;
}

/* 筛选选项 */
.filter-options {
  display: flex;
  gap: 16rpx;
  margin-bottom: 48rpx;
  justify-content: center;
}

.filter-btn {
  padding: 20rpx 40rpx;
  border: 2rpx solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  font-size: 24rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1rpx;
  transition: all 0.2s ease;
}

.filter-btn.active {
  background-color: var(--text-color);
  color: var(--bg-color);
}

/* 故事列表 */
.stories-list {
  margin-bottom: 64rpx;
}

.story-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid var(--border-color);
  transition: all 0.2s ease;
  gap: 32rpx;
}

.story-item:hover {
  background-color: var(--hover-bg);
  transform: translateX(8rpx);
}

.story-item:last-child {
  border-bottom: none;
}

.story-number {
  font-size: 32rpx;
  font-weight: 900;
  color: var(--secondary-text);
  min-width: 64rpx;
  text-align: center;
}

.story-thumbnail {
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid var(--border-color);
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.theme-dark .story-thumbnail {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
}

.story-emoji-small {
  font-size: 32rpx;
}

.story-info {
  flex: 1;
  min-width: 0;
}

.story-title-small {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.story-preview {
  font-size: 24rpx;
  color: var(--secondary-text);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.story-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
}

.favorite-btn-small {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid var(--border-color);
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.favorite-btn-small:hover {
  background-color: var(--text-color);
  transform: scale(1.1);
}

.read-indicator {
  font-size: 20rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1rpx;
  color: var(--secondary-text);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 128rpx 32rpx;
}

.empty-emoji {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: var(--secondary-text);
  margin-bottom: 48rpx;
}

/* 统计信息 */
.stats {
  display: flex;
  justify-content: space-around;
  padding: 48rpx 32rpx;
  border-top: 2rpx solid var(--border-color);
  margin-top: 32rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 900;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: var(--secondary-text);
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

/* 响应式 */
@media (max-width: 768rpx) {
  .story-item {
    padding: 24rpx 16rpx;
    gap: 16rpx;
  }
  
  .story-number {
    min-width: 48rpx;
    font-size: 28rpx;
  }
  
  .story-thumbnail {
    width: 64rpx;
    height: 64rpx;
  }
  
  .story-emoji-small {
    font-size: 28rpx;
  }
  
  .story-title-small {
    font-size: 28rpx;
  }
  
  .story-preview {
    font-size: 22rpx;
  }
}
