'use client';

import { useState } from 'react';

interface CatStory {
  id: number;
  title: string;
  content: string;
  emoji: string;
  color: string;
}

interface StoryCardProps {
  story: CatStory;
  onClick: () => void;
  isFavorite?: boolean;
  onToggleFavorite?: () => void;
}

export default function StoryCard({ story, onClick, isFavorite = false, onToggleFavorite }: StoryCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`${story.color} rounded-2xl p-6 cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl relative overflow-hidden`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Favorite button */}
      {onToggleFavorite && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite();
          }}
          className="absolute top-3 right-3 z-20 p-2 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 transition-all duration-200 hover:scale-110"
          aria-label={isFavorite ? "取消收藏" : "添加收藏"}
        >
          <span className={`text-xl ${isFavorite ? 'text-red-500' : 'text-gray-400'}`}>
            {isFavorite ? '❤️' : '🤍'}
          </span>
        </button>
      )}

      {/* Decorative elements */}
      <div className="absolute top-2 left-2 opacity-20">
        <div className="text-2xl">🐾</div>
      </div>
      <div className="absolute bottom-2 left-2 opacity-20">
        <div className="text-xl">✨</div>
      </div>
      
      <div className="text-center relative z-10">
        <div className={`text-6xl mb-4 transition-transform duration-300 ${isHovered ? 'scale-110 wiggle' : ''}`}>
          {story.emoji}
        </div>
        <h3 className="text-xl font-bold text-gray-800 mb-3">
          {story.title}
        </h3>
        <p className="text-gray-700 text-sm line-clamp-3 mb-4">
          {story.content.substring(0, 100)}...
        </p>
        <button 
          className={`bg-white bg-opacity-70 hover:bg-opacity-100 px-4 py-2 rounded-full text-sm font-medium text-gray-800 transition-all duration-200 ${isHovered ? 'bounce-gentle' : ''}`}
        >
          阅读故事 📖
        </button>
      </div>
      
      {/* Hover effect overlay */}
      {isHovered && (
        <div className="absolute inset-0 bg-white bg-opacity-10 rounded-2xl pointer-events-none" />
      )}
    </div>
  );
}
