@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Arial', 'Helvetica', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  line-height: 1.6;
}

/* Magazine Typography */
.font-serif {
  font-family: 'Georgia', 'Times New Roman', serif;
}

.font-black {
  font-weight: 900;
}

/* Magazine Layout Utilities */
.tracking-widest {
  letter-spacing: 0.1em;
}

.tracking-wide {
  letter-spacing: 0.05em;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

/* Print-style borders */
.border-thick {
  border-width: 3px;
}

/* Custom animations for cute effects */
@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

.bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.wiggle {
  animation: wiggle 1s ease-in-out infinite;
}

/* Line clamp utility for text truncation */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
