/**
 * API 接口封装
 * 统一处理网络请求、错误处理、loading状态等
 */

// API 配置
const API_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'https://dev-api.catstories.com',
    timeout: 10000
  },
  // 生产环境
  production: {
    baseURL: 'https://api.catstories.com',
    timeout: 15000
  }
};

// 获取当前环境配置
const ENV = 'development'; // 可以根据实际情况动态设置
const config = API_CONFIG[ENV];

/**
 * 网络请求封装
 */
class ApiService {
  constructor() {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout;
    this.token = wx.getStorageSync('token') || '';
  }

  /**
   * 通用请求方法
   */
  request(options) {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      showLoading = true,
      loadingText = '加载中...'
    } = options;

    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    // 构建请求头
    const requestHeader = {
      'Content-Type': 'application/json',
      ...header
    };

    // 添加认证token
    if (this.token) {
      requestHeader['Authorization'] = `Bearer ${this.token}`;
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseURL}${url}`,
        method,
        data,
        header: requestHeader,
        timeout: this.timeout,
        success: (res) => {
          if (showLoading) {
            wx.hideLoading();
          }

          // 处理响应
          this.handleResponse(res, resolve, reject);
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }

          // 处理网络错误
          this.handleNetworkError(error, reject);
        }
      });
    });
  }

  /**
   * 处理响应数据
   */
  handleResponse(res, resolve, reject) {
    const { statusCode, data } = res;

    // HTTP状态码检查
    if (statusCode >= 200 && statusCode < 300) {
      // 业务状态码检查
      if (data.code === 0 || data.success) {
        resolve(data);
      } else {
        // 业务错误
        this.handleBusinessError(data, reject);
      }
    } else {
      // HTTP错误
      this.handleHttpError(statusCode, reject);
    }
  }

  /**
   * 处理业务错误
   */
  handleBusinessError(data, reject) {
    const error = {
      type: 'BUSINESS_ERROR',
      code: data.code,
      message: data.message || '业务处理失败'
    };

    // 特殊错误处理
    if (data.code === 401) {
      this.handleAuthError();
    }

    wx.showToast({
      title: error.message,
      icon: 'none',
      duration: 2000
    });

    reject(error);
  }

  /**
   * 处理HTTP错误
   */
  handleHttpError(statusCode, reject) {
    let message = '网络请求失败';

    switch (statusCode) {
      case 400:
        message = '请求参数错误';
        break;
      case 401:
        message = '未授权，请重新登录';
        this.handleAuthError();
        break;
      case 403:
        message = '拒绝访问';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 500:
        message = '服务器内部错误';
        break;
      case 502:
        message = '网关错误';
        break;
      case 503:
        message = '服务不可用';
        break;
      default:
        message = `网络错误 (${statusCode})`;
    }

    const error = {
      type: 'HTTP_ERROR',
      code: statusCode,
      message
    };

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    reject(error);
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error, reject) {
    let message = '网络连接失败';

    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        message = '请求超时，请检查网络连接';
      } else if (error.errMsg.includes('fail')) {
        message = '网络连接失败，请检查网络设置';
      }
    }

    const networkError = {
      type: 'NETWORK_ERROR',
      message,
      originalError: error
    };

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    reject(networkError);
  }

  /**
   * 处理认证错误
   */
  handleAuthError() {
    // 清除本地token
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    this.token = '';

    // 跳转到登录页面
    wx.showModal({
      title: '登录过期',
      content: '您的登录已过期，请重新登录',
      showCancel: false,
      confirmText: '去登录',
      success: () => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }
    });
  }

  /**
   * 设置token
   */
  setToken(token) {
    this.token = token;
    wx.setStorageSync('token', token);
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request({
      url: fullUrl,
      method: 'GET',
      ...options
    });
  }

  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }

  /**
   * 文件上传
   */
  upload(url, filePath, formData = {}, options = {}) {
    const { showLoading = true, loadingText = '上传中...' } = options;

    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: `${this.baseURL}${url}`,
        filePath,
        name: 'file',
        formData,
        header: {
          'Authorization': this.token ? `Bearer ${this.token}` : ''
        },
        success: (res) => {
          if (showLoading) {
            wx.hideLoading();
          }

          try {
            const data = JSON.parse(res.data);
            if (data.code === 0 || data.success) {
              resolve(data);
            } else {
              reject(data);
            }
          } catch (error) {
            reject({ message: '响应数据解析失败' });
          }
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }
          reject(error);
        }
      });
    });
  }
}

// 创建API实例
const api = new ApiService();

export default api;
