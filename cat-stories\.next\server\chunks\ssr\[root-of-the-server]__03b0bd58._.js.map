{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/ShareButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface ShareButtonProps {\n  title: string;\n  text: string;\n  url?: string;\n}\n\nexport default function ShareButton({ title, text, url = window.location.href }: ShareButtonProps) {\n  const [showToast, setShowToast] = useState(false);\n\n  const handleShare = async () => {\n    const shareData = {\n      title,\n      text,\n      url\n    };\n\n    try {\n      if (navigator.share) {\n        await navigator.share(shareData);\n      } else {\n        // Fallback: copy to clipboard\n        await navigator.clipboard.writeText(`${title}\\n${text}\\n${url}`);\n        setShowToast(true);\n        setTimeout(() => setShowToast(false), 2000);\n      }\n    } catch (error) {\n      console.error('Error sharing:', error);\n      // Fallback: copy to clipboard\n      try {\n        await navigator.clipboard.writeText(`${title}\\n${text}\\n${url}`);\n        setShowToast(true);\n        setTimeout(() => setShowToast(false), 2000);\n      } catch (clipboardError) {\n        console.error('Error copying to clipboard:', clipboardError);\n      }\n    }\n  };\n\n  return (\n    <>\n      <button\n        onClick={handleShare}\n        className=\"p-2 border-2 border-black dark:border-white text-black dark:text-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200\"\n        aria-label=\"分享故事\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\" />\n        </svg>\n      </button>\n\n      {/* Toast notification */}\n      {showToast && (\n        <div className=\"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-black dark:bg-white text-white dark:text-black px-6 py-3 font-bold uppercase tracking-wide text-sm shadow-lg z-50 animate-in slide-in-from-bottom duration-200\">\n          已复制到剪贴板\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,OAAO,QAAQ,CAAC,IAAI,EAAoB;IAC/F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAAc;QAClB,MAAM,YAAY;YAChB;YACA;YACA;QACF;QAEA,IAAI;YACF,IAAI,UAAU,KAAK,EAAE;gBACnB,MAAM,UAAU,KAAK,CAAC;YACxB,OAAO;gBACL,8BAA8B;gBAC9B,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK;gBAC/D,aAAa;gBACb,WAAW,IAAM,aAAa,QAAQ;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,8BAA8B;YAC9B,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK;gBAC/D,aAAa;gBACb,WAAW,IAAM,aAAa,QAAQ;YACxC,EAAE,OAAO,gBAAgB;gBACvB,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;IACF;IAEA,qBACE;;0BACE,8OAAC;gBACC,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;YAKxE,2BACC,8OAAC;gBAAI,WAAU;0BAAuN;;;;;;;;AAM9O", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/StoryModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport ShareButton from './ShareButton';\nimport StoryIllustration from './StoryIllustration';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\ninterface StoryModalProps {\n  story: CatStory | null;\n  onClose: () => void;\n}\n\nexport default function StoryModal({ story, onClose }: StoryModalProps) {\n  useEffect(() => {\n    if (story) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [story]);\n\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (story) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [story, onClose]);\n\n  if (!story) return null;\n\n  return (\n    <div\n      className=\"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200\"\n      onClick={onClose}\n    >\n      <div\n        className=\"bg-white dark:bg-gray-900 max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-in zoom-in-95 duration-200 border-4 border-black dark:border-white\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Magazine Article Header */}\n        <header className=\"border-b-2 border-black dark:border-white p-8\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"flex-1\">\n              <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2\">\n                特别故事\n              </div>\n              <h1 className=\"text-3xl md:text-4xl lg:text-5xl font-black text-black dark:text-white mb-6 leading-tight\">\n                {story.title}\n              </h1>\n              <div className=\"mb-6 h-48 md:h-64\">\n                <StoryIllustration\n                  storyId={story.id}\n                  title={story.title}\n                  emoji={story.emoji}\n                  className=\"w-full h-full\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-2 ml-4\">\n              <ShareButton\n                title={`${story.title}`}\n                text={story.content.substring(0, 100) + '...'}\n              />\n              <button\n                onClick={onClose}\n                className=\"text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 text-2xl font-bold w-10 h-10 flex items-center justify-center transition-all duration-200\"\n                aria-label=\"关闭故事\"\n              >\n                ×\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Magazine Article Content */}\n        <article className=\"p-8\">\n          <div className=\"prose prose-lg max-w-none\">\n            <p className=\"text-gray-800 dark:text-gray-200 leading-relaxed text-lg font-serif first-letter:text-6xl first-letter:font-black first-letter:text-black dark:first-letter:text-white first-letter:float-left first-letter:mr-3 first-letter:mt-2 first-letter:leading-none\">\n              {story.content}\n            </p>\n          </div>\n\n          {/* Article End */}\n          <div className=\"mt-12 pt-8 border-t border-gray-300 dark:border-gray-600 text-center\">\n            <div className=\"inline-block bg-black dark:bg-white text-white dark:text-black px-6 py-2 font-bold uppercase tracking-wide text-sm mb-4\">\n              故事完\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 text-sm italic\">\n              \"每一个故事都是一段温暖的旅程\"\n            </p>\n          </div>\n        </article>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAmBe,SAAS,WAAW,EAAE,KAAK,EAAE,OAAO,EAAmB;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,OAAO;YACT,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,8OAAC;YACC,WAAU;YACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8BAGjC,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoF;;;;;;kDAGnG,8OAAC;wCAAG,WAAU;kDACX,MAAM,KAAK;;;;;;kDAEd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,uIAAA,CAAA,UAAiB;4CAChB,SAAS,MAAM,EAAE;4CACjB,OAAO,MAAM,KAAK;4CAClB,OAAO,MAAM,KAAK;4CAClB,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAO,GAAG,MAAM,KAAK,EAAE;wCACvB,MAAM,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;;;;;;kDAE1C,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDACZ;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,MAAM,OAAO;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA0H;;;;;;8CAGzI,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void;\n  placeholder?: string;\n}\n\nexport default function SearchBar({ onSearch, placeholder = \"搜索猫咪故事...\" }: SearchBarProps) {\n  const [query, setQuery] = useState('');\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSearch(query);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setQuery(value);\n    onSearch(value); // Real-time search\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <div className=\"text-center mb-4\">\n        <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400\">\n          搜索故事\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"relative\">\n        <input\n          type=\"text\"\n          value={query}\n          onChange={handleChange}\n          placeholder={placeholder}\n          className=\"w-full px-6 py-4 pl-14 pr-14 text-lg text-black dark:text-white bg-white dark:bg-gray-800 border-2 border-black dark:border-white focus:outline-none focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600 transition-all duration-200 font-medium\"\n        />\n\n        <div className=\"absolute left-4 top-1/2 transform -translate-y-1/2\">\n          <svg\n            className=\"w-6 h-6 text-black dark:text-white\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            />\n          </svg>\n        </div>\n\n        {query && (\n          <button\n            type=\"button\"\n            onClick={() => {\n              setQuery('');\n              onSearch('');\n            }}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 p-1 transition-colors duration-200\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        )}\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,WAAW,EAAkB;IACvF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,SAAS,QAAQ,mBAAmB;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAA+E;;;;;;;;;;;0BAKhG,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;oBAKP,uBACC,8OAAC;wBACC,MAAK;wBACL,SAAS;4BACP,SAAS;4BACT,SAAS;wBACX;wBACA,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [isDark, setIsDark] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDark;\n    setIsDark(newTheme);\n    \n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className=\"fixed top-4 right-4 z-50 p-3 bg-black dark:bg-white text-white dark:text-black border-2 border-black dark:border-white hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 font-bold\"\n      aria-label={isDark ? '切换到亮色模式' : '切换到暗色模式'}\n    >\n      <span className=\"text-lg\">\n        {isDark ? '☀️' : '🌙'}\n      </span>\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4DAA4D;QAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;QAE7E,IAAI,eAAe,UAAW,CAAC,cAAc,aAAc;YACzD,UAAU;YACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,CAAC;QAClB,UAAU;QAEV,IAAI,UAAU;YACZ,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,SAAS,YAAY;kBAEjC,cAAA,8OAAC;YAAK,WAAU;sBACb,SAAS,OAAO;;;;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/TableOfContents.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\ninterface TableOfContentsProps {\n  stories: CatStory[];\n  onStorySelect: (story: CatStory) => void;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function TableOfContents({ stories, onStorySelect, isOpen, onClose }: TableOfContentsProps) {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200\">\n      <div className=\"bg-white dark:bg-gray-900 max-w-4xl w-full max-h-[90vh] overflow-y-auto border-4 border-black dark:border-white\">\n        {/* Header */}\n        <header className=\"border-b-2 border-black dark:border-white p-8\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2\">\n                本期目录\n              </div>\n              <h2 className=\"text-3xl md:text-4xl font-black text-black dark:text-white\">\n                CAT STORIES\n              </h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n                第1期 • 2024年冬季刊 • 共{stories.length}篇故事\n              </p>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 text-2xl font-bold w-10 h-10 flex items-center justify-center transition-all duration-200\"\n            >\n              ×\n            </button>\n          </div>\n        </header>\n\n        {/* Contents */}\n        <div className=\"p-8\">\n          <div className=\"grid gap-4\">\n            {stories.map((story, index) => (\n              <article\n                key={story.id}\n                className=\"border-b border-gray-300 dark:border-gray-600 pb-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-4 transition-colors duration-200\"\n                onClick={() => {\n                  onStorySelect(story);\n                  onClose();\n                }}\n              >\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"text-2xl font-black text-gray-400 dark:text-gray-500 w-8\">\n                    {String(index + 1).padStart(2, '0')}\n                  </div>\n                  <div className=\"text-3xl\">\n                    {story.emoji}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-xl font-bold text-black dark:text-white mb-1\">\n                      {story.title}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400 line-clamp-2\">\n                      {story.content.substring(0, 100)}...\n                    </p>\n                  </div>\n                  <div className=\"text-xs font-bold uppercase tracking-wide text-gray-500 dark:text-gray-400\">\n                    阅读 →\n                  </div>\n                </div>\n              </article>\n            ))}\n          </div>\n\n          {/* Footer */}\n          <div className=\"mt-8 pt-8 border-t border-gray-300 dark:border-gray-600 text-center\">\n            <p className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400\">\n              点击任意故事开始阅读\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAmBe,SAAS,gBAAgB,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvG,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAoF;;;;;;kDAGnG,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAE,WAAU;;4CAAgD;4CACxC,QAAQ,MAAM;4CAAC;;;;;;;;;;;;;0CAGtC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,8OAAC;oCAEC,WAAU;oCACV,SAAS;wCACP,cAAc;wCACd;oCACF;8CAEA,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK;;;;;;0DAEd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,8OAAC;wDAAE,WAAU;;4DACV,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;4DAAK;;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;0DAA6E;;;;;;;;;;;;mCAtBzF,MAAM,EAAE;;;;;;;;;;sCA+BnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA+E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/hooks/useFavorites.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useFavorites() {\n  const [favorites, setFavorites] = useState<number[]>([]);\n\n  // Load favorites from localStorage on mount\n  useEffect(() => {\n    const saved = localStorage.getItem('cat-stories-favorites');\n    if (saved) {\n      try {\n        setFavorites(JSON.parse(saved));\n      } catch (error) {\n        console.error('Error loading favorites:', error);\n      }\n    }\n  }, []);\n\n  // Save favorites to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cat-stories-favorites', JSON.stringify(favorites));\n  }, [favorites]);\n\n  const addToFavorites = (storyId: number) => {\n    setFavorites(prev => [...prev, storyId]);\n  };\n\n  const removeFromFavorites = (storyId: number) => {\n    setFavorites(prev => prev.filter(id => id !== storyId));\n  };\n\n  const toggleFavorite = (storyId: number) => {\n    if (favorites.includes(storyId)) {\n      removeFromFavorites(storyId);\n    } else {\n      addToFavorites(storyId);\n    }\n  };\n\n  const isFavorite = (storyId: number) => {\n    return favorites.includes(storyId);\n  };\n\n  return {\n    favorites,\n    addToFavorites,\n    removeFromFavorites,\n    toggleFavorite,\n    isFavorite\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,IAAI;gBACF,aAAa,KAAK,KAAK,CAAC;YAC1B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;IACF,GAAG,EAAE;IAEL,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;IAC/D,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IACzC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,QAAQ,CAAC,UAAU;YAC/B,oBAAoB;QACtB,OAAO;YACL,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,UAAU,QAAQ,CAAC;IAC5B;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport StoryCard from '@/components/StoryCard';\nimport StoryModal from '@/components/StoryModal';\nimport CatIcon from '@/components/CatIcon';\nimport SearchBar from '@/components/SearchBar';\nimport ThemeToggle from '@/components/ThemeToggle';\nimport TableOfContents from '@/components/TableOfContents';\nimport StoryIllustration from '@/components/StoryIllustration';\nimport { useFavorites } from '@/hooks/useFavorites';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\nconst catStories: CatStory[] = [\n  {\n    id: 1,\n    title: \"小橘猫的冒险\",\n    content: \"从前有一只可爱的小橘猫叫做咪咪，它住在一个温馨的小屋里。每天早上，咪咪都会在阳光下伸懒腰，然后开始它的日常冒险。今天，咪咪发现了花园里有一只美丽的蝴蝶，它决定跟着蝴蝶去探索未知的世界...\",\n    emoji: \"🐱\",\n    color: \"bg-gradient-to-br from-orange-200 to-orange-300\"\n  },\n  {\n    id: 2,\n    title: \"月光下的白猫\",\n    content: \"在一个宁静的夜晚，一只雪白的猫咪悄悄地走在月光下。它的毛发在月光的照耀下闪闪发光，就像天使一样美丽。白猫喜欢在夜晚漫步，因为这时候世界特别安静，它可以听到自己内心的声音...\",\n    emoji: \"🌙\",\n    color: \"bg-gradient-to-br from-blue-200 to-purple-300\"\n  },\n  {\n    id: 3,\n    title: \"花园里的小黑猫\",\n    content: \"小黑猫露娜最喜欢在花园里玩耍。它会在玫瑰花丛中捉迷藏，在薰衣草田里打滚，还会和蜜蜂做朋友。虽然其他动物有时候会害怕它的黑色毛发，但露娜其实是最温柔善良的猫咪...\",\n    emoji: \"🌸\",\n    color: \"bg-gradient-to-br from-pink-200 to-rose-300\"\n  },\n  {\n    id: 4,\n    title: \"爱睡觉的灰猫\",\n    content: \"灰猫咕咕是世界上最爱睡觉的猫咪。它可以在任何地方睡着：阳光下的窗台、柔软的沙发、甚至是主人的键盘上。咕咕的梦境总是充满了鱼和毛线球，每次醒来都会满足地打个哈欠...\",\n    emoji: \"😴\",\n    color: \"bg-gradient-to-br from-gray-200 to-slate-300\"\n  },\n  {\n    id: 5,\n    title: \"勇敢的虎斑猫\",\n    content: \"虎斑猫托尼是社区里最勇敢的猫咪。当其他小动物遇到困难时，托尼总是第一个站出来帮助。它曾经救过掉进水池的小鸟，也帮助过迷路的小老鼠找到回家的路。大家都很敬佩托尼的勇气和善良...\",\n    emoji: \"🦸\",\n    color: \"bg-gradient-to-br from-yellow-200 to-amber-300\"\n  },\n  {\n    id: 6,\n    title: \"爱吃鱼的小花猫\",\n    content: \"小花猫咪咪最大的爱好就是吃鱼。它知道镇上每一家鱼店的开门时间，也认识所有卖鱼的叔叔阿姨。咪咪特别聪明，它学会了用可爱的眼神和甜美的叫声来获得美味的鱼儿。每天的鱼餐时间是咪咪最快乐的时光...\",\n    emoji: \"🐟\",\n    color: \"bg-gradient-to-br from-teal-200 to-cyan-300\"\n  },\n  {\n    id: 7,\n    title: \"会弹钢琴的音乐猫\",\n    content: \"在一个音乐世家里住着一只特别的猫咪叫做莫扎特。它从小就对音乐有着天生的敏感，经常在主人练琴时静静地坐在旁边聆听。有一天，莫扎特竟然学会了用爪子弹奏简单的旋律，成为了世界上第一只会弹钢琴的猫咪...\",\n    emoji: \"🎹\",\n    color: \"bg-gradient-to-br from-indigo-200 to-blue-300\"\n  },\n  {\n    id: 8,\n    title: \"爱画画的艺术猫\",\n    content: \"小艺是一只充满创意的猫咪，它最喜欢用爪子蘸颜料在画布上创作。它的作品色彩斑斓，充满了猫咪独特的视角和想象力。小艺的画作在当地艺术展上大受欢迎，人们都惊叹于这只猫咪的艺术天赋...\",\n    emoji: \"🎨\",\n    color: \"bg-gradient-to-br from-red-200 to-pink-300\"\n  },\n  {\n    id: 9,\n    title: \"爱读书的学者猫\",\n    content: \"图书馆里住着一只博学的猫咪叫做牛顿。它每天都会坐在书架旁，似乎在认真地'阅读'各种书籍。虽然猫咪不会真的读书，但牛顿对知识的渴望和对学习的热爱感染了每一个来图书馆的人...\",\n    emoji: \"📚\",\n    color: \"bg-gradient-to-br from-green-200 to-emerald-300\"\n  }\n];\n\nexport default function Home() {\n  const [selectedStory, setSelectedStory] = useState<CatStory | null>(null);\n  const [currentPage, setCurrentPage] = useState(0);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);\n  const [showTableOfContents, setShowTableOfContents] = useState(false);\n  const { toggleFavorite, isFavorite, favorites } = useFavorites();\n  const storiesPerPage = 3;\n\n  // Filter stories based on search query and favorites\n  const filteredStories = useMemo(() => {\n    let stories = catStories;\n\n    // Filter by favorites if enabled\n    if (showFavoritesOnly) {\n      stories = stories.filter(story => favorites.includes(story.id));\n    }\n\n    // Filter by search query\n    if (searchQuery.trim()) {\n      stories = stories.filter(story =>\n        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        story.content.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    return stories;\n  }, [searchQuery, showFavoritesOnly, favorites]);\n\n  const totalPages = Math.ceil(filteredStories.length / storiesPerPage);\n  const currentStories = filteredStories.slice(\n    currentPage * storiesPerPage,\n    (currentPage + 1) * storiesPerPage\n  );\n\n  // Reset to first page when search changes\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    setCurrentPage(0);\n  };\n\n  // Toggle favorites filter\n  const handleToggleFavoritesFilter = () => {\n    setShowFavoritesOnly(!showFavoritesOnly);\n    setCurrentPage(0);\n  };\n\n  // Random story function\n  const handleRandomStory = () => {\n    const randomIndex = Math.floor(Math.random() * catStories.length);\n    setSelectedStory(catStories[randomIndex]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\">\n      {/* Theme Toggle */}\n      <ThemeToggle />\n\n      {/* Magazine Header */}\n      <header className=\"border-b-4 border-black dark:border-white bg-white dark:bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto px-4 py-6\">\n          {/* Magazine Title */}\n          <div className=\"text-center mb-6\">\n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight text-black dark:text-white mb-2\">\n              CAT STORIES\n            </h1>\n            <div className=\"flex items-center justify-center gap-4 text-sm font-bold uppercase tracking-widest text-gray-600 dark:text-gray-400\">\n              <span>第1期</span>\n              <span>•</span>\n              <span>2024年冬季刊</span>\n              <span>•</span>\n              <span>温馨猫咪专辑</span>\n            </div>\n          </div>\n\n          {/* Magazine Subtitle */}\n          <div className=\"text-center border-t border-b border-gray-300 dark:border-gray-600 py-4 mb-6\">\n            <p className=\"text-lg md:text-xl font-serif italic text-gray-700 dark:text-gray-300\">\n              \"每一个故事都是一段温暖的旅程\"\n            </p>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <button\n              onClick={() => setShowTableOfContents(true)}\n              className=\"w-full sm:w-auto border-2 border-black dark:border-white text-black dark:text-white px-8 py-3 font-bold uppercase tracking-wide hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200\"\n            >\n              目录\n            </button>\n\n            <button\n              onClick={handleRandomStory}\n              className=\"w-full sm:w-auto bg-black dark:bg-white text-white dark:text-black px-8 py-3 font-bold uppercase tracking-wide hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n            >\n              随机阅读\n            </button>\n\n            <button\n              onClick={handleToggleFavoritesFilter}\n              className={`w-full sm:w-auto px-8 py-3 font-bold uppercase tracking-wide transition-all duration-200 ${\n                showFavoritesOnly\n                  ? 'bg-red-600 text-white hover:bg-red-700'\n                  : 'border-2 border-black dark:border-white text-black dark:text-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black'\n              }`}\n            >\n              {showFavoritesOnly ? '显示全部' : '我的收藏'} ({favorites.length})\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Story Modal */}\n      <StoryModal\n        story={selectedStory}\n        onClose={() => setSelectedStory(null)}\n      />\n\n      {/* Table of Contents */}\n      <TableOfContents\n        stories={catStories}\n        onStorySelect={setSelectedStory}\n        isOpen={showTableOfContents}\n        onClose={() => setShowTableOfContents(false)}\n      />\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 py-8\">\n        {/* Search Bar */}\n        <div className=\"mb-8\">\n          <SearchBar onSearch={handleSearch} />\n        </div>\n\n        {/* Search Results Info */}\n        {searchQuery && (\n          <div className=\"text-center mb-8 px-4\">\n            <div className=\"inline-block bg-black dark:bg-white text-white dark:text-black px-4 py-2 font-bold uppercase tracking-wide text-sm\">\n              搜索结果: {filteredStories.length} 篇关于 \"{searchQuery}\" 的故事\n            </div>\n          </div>\n        )}\n\n        {/* Magazine Layout */}\n        {currentStories.length > 0 ? (\n          <div className=\"space-y-12\">\n            {/* Featured Story (First Story) */}\n            {currentPage === 0 && currentStories.length > 0 && (\n              <article className=\"border-b-2 border-black dark:border-white pb-12 mb-12\">\n                <div className=\"grid lg:grid-cols-2 gap-8 items-center\">\n                  <div>\n                    <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2\">\n                      本期特稿\n                    </div>\n                    <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-black mb-4 text-black dark:text-white leading-tight\">\n                      {currentStories[0].title}\n                    </h2>\n                    <p className=\"text-lg text-gray-700 dark:text-gray-300 mb-6 leading-relaxed\">\n                      {currentStories[0].content.substring(0, 200)}...\n                    </p>\n                    <div className=\"flex items-center gap-4\">\n                      <button\n                        onClick={() => setSelectedStory(currentStories[0])}\n                        className=\"bg-black dark:bg-white text-white dark:text-black px-6 py-3 font-bold uppercase tracking-wide hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n                      >\n                        阅读全文\n                      </button>\n                      <button\n                        onClick={() => toggleFavorite(currentStories[0].id)}\n                        className=\"p-3 border-2 border-black dark:border-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200\"\n                      >\n                        {isFavorite(currentStories[0].id) ? '❤️' : '🤍'}\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"h-64 md:h-80\">\n                    <StoryIllustration\n                      storyId={currentStories[0].id}\n                      title={currentStories[0].title}\n                      emoji={currentStories[0].emoji}\n                      className=\"w-full h-full\"\n                    />\n                  </div>\n                </div>\n              </article>\n            )}\n\n            {/* Other Stories Grid */}\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {currentStories.slice(currentPage === 0 ? 1 : 0).map((story, index) => (\n                <article key={story.id} className=\"border-b border-gray-300 dark:border-gray-600 pb-6\">\n                  <div className=\"mb-4 h-32\">\n                    <StoryIllustration\n                      storyId={story.id}\n                      title={story.title}\n                      emoji={story.emoji}\n                      className=\"w-full h-full\"\n                    />\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-black dark:text-white\">\n                    {story.title}\n                  </h3>\n                  <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed\">\n                    {story.content.substring(0, 120)}...\n                  </p>\n                  <div className=\"flex items-center justify-between\">\n                    <button\n                      onClick={() => setSelectedStory(story)}\n                      className=\"text-xs font-bold uppercase tracking-wide text-black dark:text-white hover:underline\"\n                    >\n                      阅读更多 →\n                    </button>\n                    <button\n                      onClick={() => toggleFavorite(story.id)}\n                      className=\"text-lg hover:scale-110 transition-transform duration-200\"\n                    >\n                      {isFavorite(story.id) ? '❤️' : '🤍'}\n                    </button>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center py-16 px-4\">\n            <div className=\"text-6xl mb-6\">📰</div>\n            <h3 className=\"text-2xl font-bold text-black dark:text-white mb-4\">\n              {showFavoritesOnly ? '收藏夹空空如也' : '未找到相关内容'}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {showFavoritesOnly ? '开始收藏您喜欢的故事吧！' : '尝试其他搜索关键词'}\n            </p>\n          </div>\n        )}\n\n        {/* Magazine Pagination */}\n        {totalPages > 1 && (\n          <div className=\"mt-12 pt-8 border-t-2 border-black dark:border-white\">\n            <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n              <button\n                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n                disabled={currentPage === 0}\n                className=\"w-full sm:w-auto px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-bold uppercase tracking-wide disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n              >\n                ← 上一页\n              </button>\n\n              <div className=\"text-center\">\n                <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-1\">\n                  页码\n                </div>\n                <div className=\"text-2xl font-black text-black dark:text-white\">\n                  {currentPage + 1} / {totalPages}\n                </div>\n              </div>\n\n              <button\n                onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}\n                disabled={currentPage === totalPages - 1}\n                className=\"w-full sm:w-auto px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-bold uppercase tracking-wide disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n              >\n                下一页 →\n              </button>\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Magazine Footer */}\n      <footer className=\"bg-black dark:bg-white text-white dark:text-black py-12 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"grid md:grid-cols-3 gap-8 text-center md:text-left\">\n            <div>\n              <h3 className=\"text-xl font-black uppercase tracking-wide mb-4\">编辑部</h3>\n              <p className=\"text-sm opacity-80\">\n                专注于分享温馨可爱的猫咪故事<br />\n                每一个故事都经过精心编写\n              </p>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-black uppercase tracking-wide mb-4\">本期内容</h3>\n              <p className=\"text-sm opacity-80\">\n                {catStories.length} 篇精选故事<br />\n                涵盖各种可爱猫咪角色\n              </p>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-black uppercase tracking-wide mb-4\">联系我们</h3>\n              <p className=\"text-sm opacity-80\">\n                感谢您的阅读与支持<br />\n                期待与您分享更多美好故事\n              </p>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-600 dark:border-gray-400 mt-8 pt-8 text-center\">\n            <p className=\"text-xs font-bold uppercase tracking-widest opacity-60\">\n              CAT STORIES MAGAZINE © 2024 • 温馨猫咪故事专刊\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAoBA,MAAM,aAAyB;IAC7B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC7D,MAAM,iBAAiB;IAEvB,qDAAqD;IACrD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,UAAU;QAEd,iCAAiC;QACjC,IAAI,mBAAmB;YACrB,UAAU,QAAQ,MAAM,CAAC,CAAA,QAAS,UAAU,QAAQ,CAAC,MAAM,EAAE;QAC/D;QAEA,yBAAyB;QACzB,IAAI,YAAY,IAAI,IAAI;YACtB,UAAU,QAAQ,MAAM,CAAC,CAAA,QACvB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEhE;QAEA,OAAO;IACT,GAAG;QAAC;QAAa;QAAmB;KAAU;IAE9C,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG;IACtD,MAAM,iBAAiB,gBAAgB,KAAK,CAC1C,cAAc,gBACd,CAAC,cAAc,CAAC,IAAI;IAGtB,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe;IACjB;IAEA,0BAA0B;IAC1B,MAAM,8BAA8B;QAClC,qBAAqB,CAAC;QACtB,eAAe;IACjB;IAEA,wBAAwB;IACxB,MAAM,oBAAoB;QACxB,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;QAChE,iBAAiB,UAAU,CAAC,YAAY;IAC1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,iIAAA,CAAA,UAAW;;;;;0BAGZ,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuG;;;;;;8CAGrH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwE;;;;;;;;;;;sCAMvF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CACX;;;;;;8CAID,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAID,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,yFAAyF,EACnG,oBACI,2CACA,gJACJ;;wCAED,oBAAoB,SAAS;wCAAO;wCAAG,UAAU,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAOjE,8OAAC,gIAAA,CAAA,UAAU;gBACT,OAAO;gBACP,SAAS,IAAM,iBAAiB;;;;;;0BAIlC,8OAAC,qIAAA,CAAA,UAAe;gBACd,SAAS;gBACT,eAAe;gBACf,QAAQ;gBACR,SAAS,IAAM,uBAAuB;;;;;;0BAIxC,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+HAAA,CAAA,UAAS;4BAAC,UAAU;;;;;;;;;;;oBAItB,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCAAqH;gCAC3H,gBAAgB,MAAM;gCAAC;gCAAO;gCAAY;;;;;;;;;;;;oBAMtD,eAAe,MAAM,GAAG,kBACvB,8OAAC;wBAAI,WAAU;;4BAEZ,gBAAgB,KAAK,eAAe,MAAM,GAAG,mBAC5C,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAoF;;;;;;8DAGnG,8OAAC;oDAAG,WAAU;8DACX,cAAc,CAAC,EAAE,CAAC,KAAK;;;;;;8DAE1B,8OAAC;oDAAE,WAAU;;wDACV,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG;wDAAK;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,iBAAiB,cAAc,CAAC,EAAE;4DACjD,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,SAAS,IAAM,eAAe,cAAc,CAAC,EAAE,CAAC,EAAE;4DAClD,WAAU;sEAET,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO;;;;;;;;;;;;;;;;;;sDAIjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uIAAA,CAAA,UAAiB;gDAChB,SAAS,cAAc,CAAC,EAAE,CAAC,EAAE;gDAC7B,OAAO,cAAc,CAAC,EAAE,CAAC,KAAK;gDAC9B,OAAO,cAAc,CAAC,EAAE,CAAC,KAAK;gDAC9B,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAQpB,8OAAC;gCAAI,WAAU;0CACZ,eAAe,KAAK,CAAC,gBAAgB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC3D,8OAAC;wCAAuB,WAAU;;0DAChC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,uIAAA,CAAA,UAAiB;oDAChB,SAAS,MAAM,EAAE;oDACjB,OAAO,MAAM,KAAK;oDAClB,OAAO,MAAM,KAAK;oDAClB,WAAU;;;;;;;;;;;0DAGd,8OAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAEd,8OAAC;gDAAE,WAAU;;oDACV,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;oDAAK;;;;;;;0DAEnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,iBAAiB;wDAChC,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,eAAe,MAAM,EAAE;wDACtC,WAAU;kEAET,WAAW,MAAM,EAAE,IAAI,OAAO;;;;;;;;;;;;;uCA1BvB,MAAM,EAAE;;;;;;;;;;;;;;;6CAkC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CACX,oBAAoB,YAAY;;;;;;0CAEnC,8OAAC;gCAAE,WAAU;0CACV,oBAAoB,iBAAiB;;;;;;;;;;;;oBAM3C,aAAa,mBACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;oCACxD,UAAU,gBAAgB;oCAC1B,WAAU;8CACX;;;;;;8CAID,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoF;;;;;;sDAGnG,8OAAC;4CAAI,WAAU;;gDACZ,cAAc;gDAAE;gDAAI;;;;;;;;;;;;;8CAIzB,8OAAC;oCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;oCACrE,UAAU,gBAAgB,aAAa;oCACvC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAE,WAAU;;gDAAqB;8DAClB,8OAAC;;;;;gDAAK;;;;;;;;;;;;;8CAIxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAE,WAAU;;gDACV,WAAW,MAAM;gDAAC;8DAAM,8OAAC;;;;;gDAAK;;;;;;;;;;;;;8CAInC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAE,WAAU;;gDAAqB;8DACvB,8OAAC;;;;;gDAAK;;;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlF", "debugId": null}}]}