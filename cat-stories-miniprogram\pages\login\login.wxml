<!-- pages/login/login.wxml -->
<view class="container theme-{{theme}}">
  <!-- 登录页面头部 -->
  <view class="login-header">
    <view class="app-logo">🐱</view>
    <view class="app-title">猫咪故事杂志</view>
    <view class="app-subtitle">温馨可爱的猫咪世界</view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="login-title">欢迎回来</view>
    <view class="login-desc">登录后可以同步您的收藏和阅读记录</view>

    <!-- 微信登录按钮 -->
    <button 
      class="login-btn wx-login-btn" 
      open-type="getUserInfo"
      bindgetuserinfo="onGetUserInfo"
      loading="{{loginLoading}}"
    >
      <view class="btn-content">
        <view class="btn-icon">📱</view>
        <view class="btn-text">微信快速登录</view>
      </view>
    </button>

    <!-- 游客模式 -->
    <button class="login-btn guest-btn" bindtap="guestLogin">
      <view class="btn-content">
        <view class="btn-icon">👤</view>
        <view class="btn-text">游客模式</view>
      </view>
    </button>

    <!-- 登录说明 -->
    <view class="login-tips">
      <view class="tip-item">✓ 同步收藏和阅读记录</view>
      <view class="tip-item">✓ 个性化推荐</view>
      <view class="tip-item">✓ 多设备数据同步</view>
    </view>
  </view>

  <!-- 隐私政策 -->
  <view class="privacy-notice">
    <text>登录即表示您同意我们的</text>
    <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
    <text>和</text>
    <text class="link" bindtap="showUserAgreement">《用户协议》</text>
  </view>
</view>
