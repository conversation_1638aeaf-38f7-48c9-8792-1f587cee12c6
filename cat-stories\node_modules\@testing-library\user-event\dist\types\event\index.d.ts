import { EventType, PointerCoords } from './types';
export type { EventType, PointerCoords };
export { dispatchEvent, dispatchUIEvent, dispatchDOMEvent } from './dispatchEvent';
export { blurElement, focusElement } from './focus';
export { input } from './input';
export type { SelectionRange } from './selection';
export { isAllSelected, modifySelectionPerMouseMove, setSelectionPerMouseDown, selectAll, } from './selection';
