import ImageManager from './ImageManager';

interface StoryIllustrationProps {
  storyId: number;
  title: string;
  emoji: string;
  className?: string;
}

// Eric <PERSON> style descriptions for each story
const illustrationDescriptions: Record<number, string> = {
  1: "小橘猫咪咪在阳光下伸懒腰，追逐着一只彩色蝴蝶穿过花园。Eric Carle风格的拼贴画效果，温暖的橙色调，手绘纸质纹理。",
  2: "雪白的猫咪在月光下优雅地漫步，毛发在银色月光中闪闪发光。深蓝和紫色的夜空，星星点点，梦幻般的Eric Carle拼贴风格。",
  3: "小黑猫露娜在五彩斑斓的花园中玩耍，被玫瑰花和薰衣草包围，与蜜蜂友好互动。明亮的粉色和绿色，Eric Carle经典的手工纸拼贴效果。",
  4: "灰色猫咪咕咕在各种舒适的地方睡觉：阳光窗台、柔软沙发。温柔的灰色调配以温暖的阳光色彩，Eric Carle风格的简单形状和纹理。",
  5: "勇敢的虎斑猫托尼帮助其他小动物，展现英雄般的姿态。金黄色和琥珀色调，动态的构图，Eric Carle标志性的大胆色彩和拼贴技法。",
  6: "小花猫咪咪被各种鱼类包围，眼神充满渴望和快乐。青绿色和蓝色的水彩效果，Eric Carle风格的海洋主题拼贴画。",
  7: "音乐猫莫扎特坐在钢琴前，爪子轻触琴键，周围飘散着音符。深蓝和靛蓝色调，音乐元素的抽象表现，Eric Carle的艺术风格。",
  8: "艺术猫小艺用爪子蘸着颜料在画布上创作，周围散落着彩色颜料。红色和粉色为主调，充满创意的混乱美感，Eric Carle拼贴风格。",
  9: "学者猫牛顿坐在高高的书架旁，被各种书籍包围，专注地"阅读"。绿色和翡翠色调，知识的象征元素，Eric Carle经典的儿童书插画风格。"
};

export default function StoryIllustration({ storyId, title, emoji, className = "" }: StoryIllustrationProps) {
  const description = illustrationDescriptions[storyId] || "Eric Carle风格的猫咪插画";

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* 尝试加载真实图片，失败则显示占位符 */}
      <ImageManager
        storyId={storyId}
        title={title}
        alt={`${title} - Eric Carle风格插画`}
        className="w-full h-full"
        showPlaceholder={true}
      />

      {/* 如果需要显示描述信息的覆盖层 */}
      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center opacity-0 hover:opacity-100">
        <div className="text-center p-4 text-white">
          <h3 className="text-lg font-bold mb-2">
            {title}
          </h3>
          <p className="text-xs italic leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}
