# 📰 CAT STORIES MAGAZINE

一个采用杂志风格设计的猫咪故事网站，使用现代化的技术栈构建。

## ✨ 特性

- 📰 **杂志风格设计** - 采用经典杂志排版，黑白配色，专业印刷风格
- 📑 **目录功能** - 完整的故事目录，方便快速浏览和选择
- 🐱 **精选猫咪故事** - 9个精心编写的温馨猫咪故事
- 🔍 **实时搜索** - 支持按标题和内容搜索故事
- ❤️ **收藏功能** - 收藏你喜欢的故事，支持本地存储
- 🎲 **随机阅读** - 点击按钮随机阅读一个故事
- 🌙 **暗色模式** - 支持亮色/暗色主题切换
- 📱 **响应式设计** - 完美适配手机、平板和桌面设备
- 📤 **分享功能** - 支持分享故事到社交媒体或复制链接
- 🎨 **专业排版** - 采用杂志级别的字体和排版设计

## 🛠️ 技术栈

- **Next.js 15** - React 全栈框架
- **React 19** - 用户界面库
- **Tailwind CSS v4.1** - 原子化 CSS 框架
- **TypeScript** - 类型安全的 JavaScript
- **Turbopack** - 快速的构建工具

## 🚀 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd cat-stories
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **打开浏览器**
   访问 [http://localhost:3000](http://localhost:3000) 查看应用

## 📁 项目结构

```
cat-stories/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── globals.css     # 全局样式
│   │   ├── layout.tsx      # 根布局
│   │   └── page.tsx        # 主页面
│   ├── components/         # React 组件
│   │   ├── CatIcon.tsx     # 猫咪图标组件
│   │   ├── SearchBar.tsx   # 搜索栏组件
│   │   ├── ShareButton.tsx # 分享按钮组件
│   │   ├── StoryCard.tsx   # 故事卡片组件
│   │   ├── StoryModal.tsx  # 故事模态框组件
│   │   └── ThemeToggle.tsx # 主题切换组件
│   └── hooks/              # 自定义 Hooks
│       └── useFavorites.ts # 收藏功能 Hook
├── public/                 # 静态资源
└── package.json           # 项目配置
```

## 🎯 主要功能

### 杂志风格界面
- 经典黑白配色方案
- 专业杂志排版布局
- 清晰的视觉层次结构
- 印刷风格的字体设计

### 故事浏览
- 特稿展示（首页大图故事）
- 网格布局的故事列表
- 完整的目录页面
- 专业的文章阅读体验

### 搜索功能
- 杂志风格的搜索界面
- 实时搜索故事标题和内容
- 搜索结果统计显示
- 支持中文搜索

### 收藏系统
- 简洁的收藏按钮设计
- 本地存储收藏状态
- 筛选显示收藏的故事
- 收藏数量统计

### 主题切换
- 支持亮色/暗色主题
- 保持杂志风格的一致性
- 平滑的主题切换动画

### 响应式设计
- 移动端优先的设计理念
- 适配各种屏幕尺寸
- 触摸友好的交互

## 🎨 设计特色

- **杂志风格** - 经典的印刷媒体设计语言
- **专业排版** - 使用专业的字体层次和间距
- **黑白美学** - 简洁有力的黑白配色方案
- **清晰布局** - 网格系统和模块化设计
- **用户友好** - 直观的界面和清晰的视觉层次
- **无障碍** - 支持键盘导航和屏幕阅读器

## 📱 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

---

🐱 用爱心制作的猫咪故事网站 🐱
