// pages/table-of-contents/table-of-contents.js
const app = getApp();

Page({
  data: {
    theme: 'light',
    allStories: [],
    displayStories: [],
    favorites: [],
    showFavoritesOnly: false,
    readingProgress: 0
  },

  onLoad() {
    this.initData();
  },

  onShow() {
    this.setData({
      theme: app.globalData.theme,
      favorites: app.globalData.favorites
    });
    this.updateDisplayStories();
    this.calculateReadingProgress();
  },

  initData() {
    const { theme, favorites, stories } = app.globalData;
    this.setData({
      theme,
      favorites,
      allStories: stories,
      displayStories: stories
    });
    this.calculateReadingProgress();
  },

  updateDisplayStories() {
    const stories = this.data.showFavoritesOnly 
      ? this.data.allStories.filter(story => this.data.favorites.includes(story.id))
      : this.data.allStories;
    
    this.setData({
      displayStories: stories
    });
  },

  calculateReadingProgress() {
    // 简单的阅读进度计算：收藏数量 / 总数量
    const progress = this.data.allStories.length > 0 
      ? Math.round((this.data.favorites.length / this.data.allStories.length) * 100)
      : 0;
    
    this.setData({
      readingProgress: progress
    });
  },

  // 主题切换
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      theme: app.globalData.theme
    });
  },

  // 收藏切换
  toggleFavorite(e) {
    const storyId = parseInt(e.currentTarget.dataset.id);
    app.toggleFavorite(storyId);
    
    this.setData({
      favorites: app.globalData.favorites
    });
    
    this.updateDisplayStories();
    this.calculateReadingProgress();
  },

  // 显示全部故事
  showAllStories() {
    this.setData({
      showFavoritesOnly: false
    });
    this.updateDisplayStories();
  },

  // 显示收藏故事
  showFavoriteStories() {
    this.setData({
      showFavoritesOnly: true
    });
    this.updateDisplayStories();
  },

  // 阅读故事
  readStory(e) {
    const story = e.currentTarget.dataset.story;
    wx.navigateTo({
      url: `/pages/story-detail/story-detail?id=${story.id}`
    });
  },

  // 判断是否收藏
  isFavorite(storyId) {
    return this.data.favorites.includes(storyId);
  },

  // 主题更新回调
  updateTheme(theme) {
    this.setData({ theme });
  }
});
