# 猫咪故事杂志 - 微信小程序版

这是一个温馨可爱的猫咪故事杂志微信小程序，包含9个精彩的猫咪故事，采用杂志风格设计，支持深色/浅色主题切换。

## ✨ 功能特性

### 📚 核心功能
- **9个温馨猫咪故事** - 完整的故事内容，适合全家阅读
- **杂志风格设计** - 专业的黑白配色和排版
- **搜索功能** - 快速查找感兴趣的故事
- **收藏系统** - 本地存储，支持收藏喜爱的故事
- **分页浏览** - 每页3个故事，优化阅读体验
- **主题切换** - 支持深色/浅色主题
- **分享功能** - 支持微信分享和朋友圈分享

### 📱 页面结构
- **首页** - 特稿展示、故事网格、搜索和筛选
- **故事详情** - 完整故事内容、大图展示、导航
- **目录页** - 所有故事列表、收藏筛选、阅读统计
- **杂志封面** - 专业杂志封面设计、特色内容预览

### 🎨 设计特色
- **Eric Carle风格** - 为每个故事准备了插画描述
- **响应式布局** - 适配不同屏幕尺寸
- **大图展示** - 特别优化的图片尺寸，特别是封面图
- **杂志美学** - 专业的排版和视觉设计

## 🚀 快速开始

### 环境要求
- 微信开发者工具
- 微信小程序开发账号

### 安装步骤

1. **下载代码**
   ```bash
   # 将整个 cat-stories-miniprogram 文件夹复制到本地
   ```

2. **导入项目**
   - 打开微信开发者工具
   - 选择"导入项目"
   - 选择 `cat-stories-miniprogram` 文件夹
   - 填入你的小程序 AppID

3. **配置项目**
   - 修改 `project.config.json` 中的 `appid` 字段
   - 根据需要调整其他配置

4. **添加图片资源**
   - 在 `images/` 目录下添加 TabBar 图标
   - 可选：添加故事插画图片

5. **预览和调试**
   - 在微信开发者工具中预览
   - 使用真机调试功能测试

## 📁 项目结构

```
cat-stories-miniprogram/
├── app.js                 # 全局应用逻辑
├── app.json              # 全局配置
├── app.wxss              # 全局样式
├── sitemap.json          # 搜索配置
├── project.config.json   # 项目配置
├── images/               # 图片资源
│   └── README.md
├── pages/                # 页面目录
│   ├── index/            # 首页
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   ├── index.js
│   │   └── index.json
│   ├── story-detail/     # 故事详情页
│   │   ├── story-detail.wxml
│   │   ├── story-detail.wxss
│   │   ├── story-detail.js
│   │   └── story-detail.json
│   ├── table-of-contents/ # 目录页
│   │   ├── table-of-contents.wxml
│   │   ├── table-of-contents.wxss
│   │   ├── table-of-contents.js
│   │   └── table-of-contents.json
│   └── magazine-cover/   # 杂志封面页
│       ├── magazine-cover.wxml
│       ├── magazine-cover.wxss
│       ├── magazine-cover.js
│       └── magazine-cover.json
└── README.md
```

## 🎯 核心技术

### 数据管理
- **全局数据** - 使用 `app.js` 管理故事数据和用户偏好
- **本地存储** - 使用 `wx.setStorageSync()` 持久化收藏和主题
- **状态同步** - 页面间数据同步和更新

### 样式系统
- **CSS变量** - 使用CSS自定义属性实现主题切换
- **响应式设计** - 媒体查询适配不同屏幕
- **杂志风格** - 专业的排版和视觉层次

### 导航系统
- **TabBar导航** - 底部标签栏快速切换
- **页面跳转** - 使用 `wx.navigateTo()` 和 `wx.switchTab()`
- **参数传递** - URL参数传递故事ID等数据

## 🔧 自定义配置

### 修改故事内容
在 `app.js` 的 `globalData.stories` 数组中修改或添加故事：

```javascript
{
  id: 10,
  title: "新故事标题",
  emoji: "🐾",
  content: "故事内容..."
}
```

### 调整主题颜色
在 `app.wxss` 中修改CSS变量：

```css
.theme-light {
  --bg-color: #ffffff;
  --text-color: #000000;
  /* 其他颜色变量 */
}
```

### 更改分页设置
在 `pages/index/index.js` 中修改 `storiesPerPage` 值。

## 📱 功能说明

### 搜索功能
- 支持标题和内容搜索
- 实时搜索结果更新
- 搜索结果高亮显示

### 收藏系统
- 点击心形图标收藏/取消收藏
- 收藏状态本地持久化
- 支持收藏筛选查看

### 主题切换
- 点击右上角月亮/太阳图标切换
- 深色/浅色主题自动适配
- 主题偏好本地保存

### 分享功能
- 支持分享到微信好友
- 支持分享到朋友圈
- 自动生成分享文案

## 🎨 插画系统

应用为每个故事准备了Eric Carle风格的插画描述，可以用于：
- AI图片生成的提示词
- 插画师创作参考
- 视觉设计指导

如需添加真实插画，请将图片放在 `images/` 目录下，命名为 `story-{id}.jpg`。

## 📄 许可证

本项目仅供学习和参考使用。故事内容为原创，请勿用于商业用途。

## 🤝 贡献

欢迎提交问题和改进建议！

## 📞 支持

如有问题，请查看：
1. 微信小程序官方文档
2. 项目README和代码注释
3. 微信开发者社区
