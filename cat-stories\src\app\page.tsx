'use client';

import { useState, useMemo } from 'react';
import StoryCard from '@/components/StoryCard';
import StoryModal from '@/components/StoryModal';
import CatIcon from '@/components/CatIcon';
import SearchBar from '@/components/SearchBar';
import { useFavorites } from '@/hooks/useFavorites';

interface CatStory {
  id: number;
  title: string;
  content: string;
  emoji: string;
  color: string;
}

const catStories: CatStory[] = [
  {
    id: 1,
    title: "小橘猫的冒险",
    content: "从前有一只可爱的小橘猫叫做咪咪，它住在一个温馨的小屋里。每天早上，咪咪都会在阳光下伸懒腰，然后开始它的日常冒险。今天，咪咪发现了花园里有一只美丽的蝴蝶，它决定跟着蝴蝶去探索未知的世界...",
    emoji: "🐱",
    color: "bg-gradient-to-br from-orange-200 to-orange-300"
  },
  {
    id: 2,
    title: "月光下的白猫",
    content: "在一个宁静的夜晚，一只雪白的猫咪悄悄地走在月光下。它的毛发在月光的照耀下闪闪发光，就像天使一样美丽。白猫喜欢在夜晚漫步，因为这时候世界特别安静，它可以听到自己内心的声音...",
    emoji: "🌙",
    color: "bg-gradient-to-br from-blue-200 to-purple-300"
  },
  {
    id: 3,
    title: "花园里的小黑猫",
    content: "小黑猫露娜最喜欢在花园里玩耍。它会在玫瑰花丛中捉迷藏，在薰衣草田里打滚，还会和蜜蜂做朋友。虽然其他动物有时候会害怕它的黑色毛发，但露娜其实是最温柔善良的猫咪...",
    emoji: "🌸",
    color: "bg-gradient-to-br from-pink-200 to-rose-300"
  },
  {
    id: 4,
    title: "爱睡觉的灰猫",
    content: "灰猫咕咕是世界上最爱睡觉的猫咪。它可以在任何地方睡着：阳光下的窗台、柔软的沙发、甚至是主人的键盘上。咕咕的梦境总是充满了鱼和毛线球，每次醒来都会满足地打个哈欠...",
    emoji: "😴",
    color: "bg-gradient-to-br from-gray-200 to-slate-300"
  },
  {
    id: 5,
    title: "勇敢的虎斑猫",
    content: "虎斑猫托尼是社区里最勇敢的猫咪。当其他小动物遇到困难时，托尼总是第一个站出来帮助。它曾经救过掉进水池的小鸟，也帮助过迷路的小老鼠找到回家的路。大家都很敬佩托尼的勇气和善良...",
    emoji: "🦸",
    color: "bg-gradient-to-br from-yellow-200 to-amber-300"
  },
  {
    id: 6,
    title: "爱吃鱼的小花猫",
    content: "小花猫咪咪最大的爱好就是吃鱼。它知道镇上每一家鱼店的开门时间，也认识所有卖鱼的叔叔阿姨。咪咪特别聪明，它学会了用可爱的眼神和甜美的叫声来获得美味的鱼儿。每天的鱼餐时间是咪咪最快乐的时光...",
    emoji: "🐟",
    color: "bg-gradient-to-br from-teal-200 to-cyan-300"
  },
  {
    id: 7,
    title: "会弹钢琴的音乐猫",
    content: "在一个音乐世家里住着一只特别的猫咪叫做莫扎特。它从小就对音乐有着天生的敏感，经常在主人练琴时静静地坐在旁边聆听。有一天，莫扎特竟然学会了用爪子弹奏简单的旋律，成为了世界上第一只会弹钢琴的猫咪...",
    emoji: "🎹",
    color: "bg-gradient-to-br from-indigo-200 to-blue-300"
  },
  {
    id: 8,
    title: "爱画画的艺术猫",
    content: "小艺是一只充满创意的猫咪，它最喜欢用爪子蘸颜料在画布上创作。它的作品色彩斑斓，充满了猫咪独特的视角和想象力。小艺的画作在当地艺术展上大受欢迎，人们都惊叹于这只猫咪的艺术天赋...",
    emoji: "🎨",
    color: "bg-gradient-to-br from-red-200 to-pink-300"
  },
  {
    id: 9,
    title: "爱读书的学者猫",
    content: "图书馆里住着一只博学的猫咪叫做牛顿。它每天都会坐在书架旁，似乎在认真地'阅读'各种书籍。虽然猫咪不会真的读书，但牛顿对知识的渴望和对学习的热爱感染了每一个来图书馆的人...",
    emoji: "📚",
    color: "bg-gradient-to-br from-green-200 to-emerald-300"
  }
];

export default function Home() {
  const [selectedStory, setSelectedStory] = useState<CatStory | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const { toggleFavorite, isFavorite, favorites } = useFavorites();
  const storiesPerPage = 3;

  // Filter stories based on search query and favorites
  const filteredStories = useMemo(() => {
    let stories = catStories;

    // Filter by favorites if enabled
    if (showFavoritesOnly) {
      stories = stories.filter(story => favorites.includes(story.id));
    }

    // Filter by search query
    if (searchQuery.trim()) {
      stories = stories.filter(story =>
        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.content.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return stories;
  }, [searchQuery, showFavoritesOnly, favorites]);

  const totalPages = Math.ceil(filteredStories.length / storiesPerPage);
  const currentStories = filteredStories.slice(
    currentPage * storiesPerPage,
    (currentPage + 1) * storiesPerPage
  );

  // Reset to first page when search changes
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(0);
  };

  // Toggle favorites filter
  const handleToggleFavoritesFilter = () => {
    setShowFavoritesOnly(!showFavoritesOnly);
    setCurrentPage(0);
  };

  // Random story function
  const handleRandomStory = () => {
    const randomIndex = Math.floor(Math.random() * catStories.length);
    setSelectedStory(catStories[randomIndex]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50">
      {/* Header */}
      <header className="text-center py-8 px-4">
        <div className="flex items-center justify-center gap-4 mb-4">
          <CatIcon size={48} className="bounce-gentle" />
          <h1 className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600">
            可爱猫咪故事集
          </h1>
          <CatIcon size={48} className="bounce-gentle" />
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
          欢迎来到温馨的猫咪世界，这里有最可爱的猫咪故事等着你来发现！
        </p>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <button
            onClick={handleRandomStory}
            className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-3 rounded-full font-medium hover:from-pink-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            🎲 随机故事
          </button>

          <button
            onClick={handleToggleFavoritesFilter}
            className={`px-6 py-3 rounded-full font-medium transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl ${
              showFavoritesOnly
                ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white hover:from-red-600 hover:to-pink-700'
                : 'bg-white text-gray-700 hover:bg-gray-50 border-2 border-pink-200'
            }`}
          >
            {showFavoritesOnly ? '❤️ 显示全部' : '🤍 只看收藏'} ({favorites.length})
          </button>
        </div>
      </header>

      {/* Story Modal */}
      <StoryModal
        story={selectedStory}
        onClose={() => setSelectedStory(null)}
      />

      {/* Main Content */}
      <main className="container mx-auto px-4 pb-8">
        {/* Search Bar */}
        <SearchBar onSearch={handleSearch} />

        {/* Search Results Info */}
        {searchQuery && (
          <div className="text-center mb-6">
            <p className="text-gray-600">
              找到 <span className="font-bold text-pink-600">{filteredStories.length}</span> 个关于
              "<span className="font-bold text-purple-600">{searchQuery}</span>" 的故事
            </p>
          </div>
        )}

        {/* Story Grid */}
        {currentStories.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {currentStories.map((story) => (
              <StoryCard
                key={story.id}
                story={story}
                onClick={() => setSelectedStory(story)}
                isFavorite={isFavorite(story.id)}
                onToggleFavorite={() => toggleFavorite(story.id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">😿</div>
            <h3 className="text-xl font-bold text-gray-600 mb-2">没有找到相关故事</h3>
            <p className="text-gray-500">试试其他关键词吧！</p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-4">
            <button
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={currentPage === 0}
              className="px-4 py-2 rounded-full bg-white shadow-md disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg transition-all duration-200"
            >
              ← 上一页
            </button>
            <span className="text-gray-600">
              第 {currentPage + 1} 页，共 {totalPages} 页
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
              disabled={currentPage === totalPages - 1}
              className="px-4 py-2 rounded-full bg-white shadow-md disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg transition-all duration-200"
            >
              下一页 →
            </button>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="text-center py-8 px-4 bg-white bg-opacity-50">
        <p className="text-gray-600 mb-2">
          🐱 用爱心制作的猫咪故事网站 🐱
        </p>
        <p className="text-sm text-gray-500">
          每一个故事都充满了温暖和快乐
        </p>
      </footer>
    </div>
  );
}
