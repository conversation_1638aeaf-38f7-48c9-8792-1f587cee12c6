'use client';

import { useState, useMemo } from 'react';
import StoryCard from '@/components/StoryCard';
import StoryModal from '@/components/StoryModal';
import CatIcon from '@/components/CatIcon';
import SearchBar from '@/components/SearchBar';
import ThemeToggle from '@/components/ThemeToggle';
import TableOfContents from '@/components/TableOfContents';
import StoryIllustration from '@/components/StoryIllustration';
import { useFavorites } from '@/hooks/useFavorites';

interface CatStory {
  id: number;
  title: string;
  content: string;
  emoji: string;
  color: string;
}

const catStories: CatStory[] = [
  {
    id: 1,
    title: "小橘猫的冒险",
    content: "从前有一只可爱的小橘猫叫做咪咪，它住在一个温馨的小屋里。每天早上，咪咪都会在阳光下伸懒腰，然后开始它的日常冒险。今天，咪咪发现了花园里有一只美丽的蝴蝶，它决定跟着蝴蝶去探索未知的世界...",
    emoji: "🐱",
    color: "bg-gradient-to-br from-orange-200 to-orange-300"
  },
  {
    id: 2,
    title: "月光下的白猫",
    content: "在一个宁静的夜晚，一只雪白的猫咪悄悄地走在月光下。它的毛发在月光的照耀下闪闪发光，就像天使一样美丽。白猫喜欢在夜晚漫步，因为这时候世界特别安静，它可以听到自己内心的声音...",
    emoji: "🌙",
    color: "bg-gradient-to-br from-blue-200 to-purple-300"
  },
  {
    id: 3,
    title: "花园里的小黑猫",
    content: "小黑猫露娜最喜欢在花园里玩耍。它会在玫瑰花丛中捉迷藏，在薰衣草田里打滚，还会和蜜蜂做朋友。虽然其他动物有时候会害怕它的黑色毛发，但露娜其实是最温柔善良的猫咪...",
    emoji: "🌸",
    color: "bg-gradient-to-br from-pink-200 to-rose-300"
  },
  {
    id: 4,
    title: "爱睡觉的灰猫",
    content: "灰猫咕咕是世界上最爱睡觉的猫咪。它可以在任何地方睡着：阳光下的窗台、柔软的沙发、甚至是主人的键盘上。咕咕的梦境总是充满了鱼和毛线球，每次醒来都会满足地打个哈欠...",
    emoji: "😴",
    color: "bg-gradient-to-br from-gray-200 to-slate-300"
  },
  {
    id: 5,
    title: "勇敢的虎斑猫",
    content: "虎斑猫托尼是社区里最勇敢的猫咪。当其他小动物遇到困难时，托尼总是第一个站出来帮助。它曾经救过掉进水池的小鸟，也帮助过迷路的小老鼠找到回家的路。大家都很敬佩托尼的勇气和善良...",
    emoji: "🦸",
    color: "bg-gradient-to-br from-yellow-200 to-amber-300"
  },
  {
    id: 6,
    title: "爱吃鱼的小花猫",
    content: "小花猫咪咪最大的爱好就是吃鱼。它知道镇上每一家鱼店的开门时间，也认识所有卖鱼的叔叔阿姨。咪咪特别聪明，它学会了用可爱的眼神和甜美的叫声来获得美味的鱼儿。每天的鱼餐时间是咪咪最快乐的时光...",
    emoji: "🐟",
    color: "bg-gradient-to-br from-teal-200 to-cyan-300"
  },
  {
    id: 7,
    title: "会弹钢琴的音乐猫",
    content: "在一个音乐世家里住着一只特别的猫咪叫做莫扎特。它从小就对音乐有着天生的敏感，经常在主人练琴时静静地坐在旁边聆听。有一天，莫扎特竟然学会了用爪子弹奏简单的旋律，成为了世界上第一只会弹钢琴的猫咪...",
    emoji: "🎹",
    color: "bg-gradient-to-br from-indigo-200 to-blue-300"
  },
  {
    id: 8,
    title: "爱画画的艺术猫",
    content: "小艺是一只充满创意的猫咪，它最喜欢用爪子蘸颜料在画布上创作。它的作品色彩斑斓，充满了猫咪独特的视角和想象力。小艺的画作在当地艺术展上大受欢迎，人们都惊叹于这只猫咪的艺术天赋...",
    emoji: "🎨",
    color: "bg-gradient-to-br from-red-200 to-pink-300"
  },
  {
    id: 9,
    title: "爱读书的学者猫",
    content: "图书馆里住着一只博学的猫咪叫做牛顿。它每天都会坐在书架旁，似乎在认真地'阅读'各种书籍。虽然猫咪不会真的读书，但牛顿对知识的渴望和对学习的热爱感染了每一个来图书馆的人...",
    emoji: "📚",
    color: "bg-gradient-to-br from-green-200 to-emerald-300"
  }
];

export default function Home() {
  const [selectedStory, setSelectedStory] = useState<CatStory | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const { toggleFavorite, isFavorite, favorites } = useFavorites();
  const storiesPerPage = 3;

  // Filter stories based on search query and favorites
  const filteredStories = useMemo(() => {
    let stories = catStories;

    // Filter by favorites if enabled
    if (showFavoritesOnly) {
      stories = stories.filter(story => favorites.includes(story.id));
    }

    // Filter by search query
    if (searchQuery.trim()) {
      stories = stories.filter(story =>
        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.content.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return stories;
  }, [searchQuery, showFavoritesOnly, favorites]);

  const totalPages = Math.ceil(filteredStories.length / storiesPerPage);
  const currentStories = filteredStories.slice(
    currentPage * storiesPerPage,
    (currentPage + 1) * storiesPerPage
  );

  // Reset to first page when search changes
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(0);
  };

  // Toggle favorites filter
  const handleToggleFavoritesFilter = () => {
    setShowFavoritesOnly(!showFavoritesOnly);
    setCurrentPage(0);
  };

  // Random story function
  const handleRandomStory = () => {
    const randomIndex = Math.floor(Math.random() * catStories.length);
    setSelectedStory(catStories[randomIndex]);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      {/* Theme Toggle */}
      <ThemeToggle />

      {/* Magazine Header */}
      <header className="border-b-4 border-black dark:border-white bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 py-6">
          {/* Magazine Title */}
          <div className="text-center mb-6">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight text-black dark:text-white mb-2">
              CAT STORIES
            </h1>
            <div className="flex items-center justify-center gap-4 text-sm font-bold uppercase tracking-widest text-gray-600 dark:text-gray-400">
              <span>第1期</span>
              <span>•</span>
              <span>2024年冬季刊</span>
              <span>•</span>
              <span>温馨猫咪专辑</span>
            </div>
          </div>

          {/* Magazine Subtitle */}
          <div className="text-center border-t border-b border-gray-300 dark:border-gray-600 py-4 mb-6">
            <p className="text-lg md:text-xl font-serif italic text-gray-700 dark:text-gray-300">
              "每一个故事都是一段温暖的旅程"
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={() => setShowTableOfContents(true)}
              className="w-full sm:w-auto border-2 border-black dark:border-white text-black dark:text-white px-8 py-3 font-bold uppercase tracking-wide hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200"
            >
              目录
            </button>

            <button
              onClick={handleRandomStory}
              className="w-full sm:w-auto bg-black dark:bg-white text-white dark:text-black px-8 py-3 font-bold uppercase tracking-wide hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
            >
              随机阅读
            </button>

            <button
              onClick={handleToggleFavoritesFilter}
              className={`w-full sm:w-auto px-8 py-3 font-bold uppercase tracking-wide transition-all duration-200 ${
                showFavoritesOnly
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'border-2 border-black dark:border-white text-black dark:text-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black'
              }`}
            >
              {showFavoritesOnly ? '显示全部' : '我的收藏'} ({favorites.length})
            </button>
          </div>
        </div>
      </header>

      {/* Story Modal */}
      <StoryModal
        story={selectedStory}
        onClose={() => setSelectedStory(null)}
      />

      {/* Table of Contents */}
      <TableOfContents
        stories={catStories}
        onStorySelect={setSelectedStory}
        isOpen={showTableOfContents}
        onClose={() => setShowTableOfContents(false)}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Search Bar */}
        <div className="mb-8">
          <SearchBar onSearch={handleSearch} />
        </div>

        {/* Search Results Info */}
        {searchQuery && (
          <div className="text-center mb-8 px-4">
            <div className="inline-block bg-black dark:bg-white text-white dark:text-black px-4 py-2 font-bold uppercase tracking-wide text-sm">
              搜索结果: {filteredStories.length} 篇关于 "{searchQuery}" 的故事
            </div>
          </div>
        )}

        {/* Magazine Layout */}
        {currentStories.length > 0 ? (
          <div className="space-y-12">
            {/* Featured Story (First Story) */}
            {currentPage === 0 && currentStories.length > 0 && (
              <article className="border-b-2 border-black dark:border-white pb-12 mb-12">
                <div className="grid lg:grid-cols-2 gap-8 items-center">
                  <div>
                    <div className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2">
                      本期特稿
                    </div>
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-black mb-4 text-black dark:text-white leading-tight">
                      {currentStories[0].title}
                    </h2>
                    <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                      {currentStories[0].content.substring(0, 200)}...
                    </p>
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => setSelectedStory(currentStories[0])}
                        className="bg-black dark:bg-white text-white dark:text-black px-6 py-3 font-bold uppercase tracking-wide hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
                      >
                        阅读全文
                      </button>
                      <button
                        onClick={() => toggleFavorite(currentStories[0].id)}
                        className="p-3 border-2 border-black dark:border-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200"
                      >
                        {isFavorite(currentStories[0].id) ? '❤️' : '🤍'}
                      </button>
                    </div>
                  </div>
                  <div className="h-64 md:h-80">
                    <StoryIllustration
                      storyId={currentStories[0].id}
                      title={currentStories[0].title}
                      emoji={currentStories[0].emoji}
                      className="w-full h-full"
                    />
                  </div>
                </div>
              </article>
            )}

            {/* Other Stories Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {currentStories.slice(currentPage === 0 ? 1 : 0).map((story, index) => (
                <article key={story.id} className="border-b border-gray-300 dark:border-gray-600 pb-6">
                  <div className="mb-4 h-32">
                    <StoryIllustration
                      storyId={story.id}
                      title={story.title}
                      emoji={story.emoji}
                      className="w-full h-full"
                    />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-black dark:text-white">
                    {story.title}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed">
                    {story.content.substring(0, 120)}...
                  </p>
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => setSelectedStory(story)}
                      className="text-xs font-bold uppercase tracking-wide text-black dark:text-white hover:underline"
                    >
                      阅读更多 →
                    </button>
                    <button
                      onClick={() => toggleFavorite(story.id)}
                      className="text-lg hover:scale-110 transition-transform duration-200"
                    >
                      {isFavorite(story.id) ? '❤️' : '🤍'}
                    </button>
                  </div>
                </article>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-16 px-4">
            <div className="text-6xl mb-6">📰</div>
            <h3 className="text-2xl font-bold text-black dark:text-white mb-4">
              {showFavoritesOnly ? '收藏夹空空如也' : '未找到相关内容'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {showFavoritesOnly ? '开始收藏您喜欢的故事吧！' : '尝试其他搜索关键词'}
            </p>
          </div>
        )}

        {/* Magazine Pagination */}
        {totalPages > 1 && (
          <div className="mt-12 pt-8 border-t-2 border-black dark:border-white">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <button
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
                className="w-full sm:w-auto px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-bold uppercase tracking-wide disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
              >
                ← 上一页
              </button>

              <div className="text-center">
                <div className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-1">
                  页码
                </div>
                <div className="text-2xl font-black text-black dark:text-white">
                  {currentPage + 1} / {totalPages}
                </div>
              </div>

              <button
                onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                disabled={currentPage === totalPages - 1}
                className="w-full sm:w-auto px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-bold uppercase tracking-wide disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
              >
                下一页 →
              </button>
            </div>
          </div>
        )}
      </main>

      {/* Magazine Footer */}
      <footer className="bg-black dark:bg-white text-white dark:text-black py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 text-center md:text-left">
            <div>
              <h3 className="text-xl font-black uppercase tracking-wide mb-4">编辑部</h3>
              <p className="text-sm opacity-80">
                专注于分享温馨可爱的猫咪故事<br />
                每一个故事都经过精心编写
              </p>
            </div>
            <div>
              <h3 className="text-xl font-black uppercase tracking-wide mb-4">本期内容</h3>
              <p className="text-sm opacity-80">
                {catStories.length} 篇精选故事<br />
                涵盖各种可爱猫咪角色
              </p>
            </div>
            <div>
              <h3 className="text-xl font-black uppercase tracking-wide mb-4">联系我们</h3>
              <p className="text-sm opacity-80">
                感谢您的阅读与支持<br />
                期待与您分享更多美好故事
              </p>
            </div>
          </div>

          <div className="border-t border-gray-600 dark:border-gray-400 mt-8 pt-8 text-center">
            <p className="text-xs font-bold uppercase tracking-widest opacity-60">
              CAT STORIES MAGAZINE © 2024 • 温馨猫咪故事专刊
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
