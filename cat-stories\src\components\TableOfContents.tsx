'use client';

import { useState } from 'react';

interface CatStory {
  id: number;
  title: string;
  content: string;
  emoji: string;
  color: string;
}

interface TableOfContentsProps {
  stories: CatStory[];
  onStorySelect: (story: CatStory) => void;
  isOpen: boolean;
  onClose: () => void;
}

export default function TableOfContents({ stories, onStorySelect, isOpen, onClose }: TableOfContentsProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200">
      <div className="bg-white dark:bg-gray-900 max-w-4xl w-full max-h-[90vh] overflow-y-auto border-4 border-black dark:border-white">
        {/* Header */}
        <header className="border-b-2 border-black dark:border-white p-8">
          <div className="flex justify-between items-center">
            <div>
              <div className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2">
                本期目录
              </div>
              <h2 className="text-3xl md:text-4xl font-black text-black dark:text-white">
                CAT STORIES
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                第1期 • 2024年冬季刊 • 共{stories.length}篇故事
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 text-2xl font-bold w-10 h-10 flex items-center justify-center transition-all duration-200"
            >
              ×
            </button>
          </div>
        </header>

        {/* Contents */}
        <div className="p-8">
          <div className="grid gap-4">
            {stories.map((story, index) => (
              <article
                key={story.id}
                className="border-b border-gray-300 dark:border-gray-600 pb-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-4 transition-colors duration-200"
                onClick={() => {
                  onStorySelect(story);
                  onClose();
                }}
              >
                <div className="flex items-center gap-4">
                  <div className="text-2xl font-black text-gray-400 dark:text-gray-500 w-8">
                    {String(index + 1).padStart(2, '0')}
                  </div>
                  <div className="text-3xl">
                    {story.emoji}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-black dark:text-white mb-1">
                      {story.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                      {story.content.substring(0, 100)}...
                    </p>
                  </div>
                  <div className="text-xs font-bold uppercase tracking-wide text-gray-500 dark:text-gray-400">
                    阅读 →
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Footer */}
          <div className="mt-8 pt-8 border-t border-gray-300 dark:border-gray-600 text-center">
            <p className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400">
              点击任意故事开始阅读
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
