/**
 * 故事相关API服务
 */
import api from '../utils/api.js';

class StoryService {
  /**
   * 获取故事列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.category - 分类
   * @param {string} params.keyword - 搜索关键词
   */
  async getStories(params = {}) {
    try {
      const response = await api.get('/api/stories', params);
      return {
        success: true,
        data: response.data,
        total: response.total,
        page: response.page,
        pageSize: response.pageSize
      };
    } catch (error) {
      console.error('获取故事列表失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 获取故事详情
   * @param {number} storyId - 故事ID
   */
  async getStoryDetail(storyId) {
    try {
      const response = await api.get(`/api/stories/${storyId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取故事详情失败:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 搜索故事
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   */
  async searchStories(keyword, options = {}) {
    try {
      const params = {
        keyword,
        page: options.page || 1,
        pageSize: options.pageSize || 10,
        ...options
      };

      const response = await api.get('/api/stories/search', params);
      return {
        success: true,
        data: response.data,
        total: response.total
      };
    } catch (error) {
      console.error('搜索故事失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 获取推荐故事
   * @param {number} count - 推荐数量
   */
  async getRecommendedStories(count = 5) {
    try {
      const response = await api.get('/api/stories/recommended', { count });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取推荐故事失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 获取热门故事
   * @param {number} count - 数量
   */
  async getPopularStories(count = 10) {
    try {
      const response = await api.get('/api/stories/popular', { count });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取热门故事失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 记录故事阅读
   * @param {number} storyId - 故事ID
   */
  async recordReading(storyId) {
    try {
      const response = await api.post('/api/stories/read', {
        storyId,
        readTime: new Date().toISOString()
      }, {
        showLoading: false // 静默请求
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('记录阅读失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取故事分类
   */
  async getCategories() {
    try {
      const response = await api.get('/api/stories/categories');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取分类失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }
}

// 创建服务实例
const storyService = new StoryService();

export default storyService;
