{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/ShareButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface ShareButtonProps {\n  title: string;\n  text: string;\n  url?: string;\n}\n\nexport default function ShareButton({ title, text, url = window.location.href }: ShareButtonProps) {\n  const [showToast, setShowToast] = useState(false);\n\n  const handleShare = async () => {\n    const shareData = {\n      title,\n      text,\n      url\n    };\n\n    try {\n      if (navigator.share) {\n        await navigator.share(shareData);\n      } else {\n        // Fallback: copy to clipboard\n        await navigator.clipboard.writeText(`${title}\\n${text}\\n${url}`);\n        setShowToast(true);\n        setTimeout(() => setShowToast(false), 2000);\n      }\n    } catch (error) {\n      console.error('Error sharing:', error);\n      // Fallback: copy to clipboard\n      try {\n        await navigator.clipboard.writeText(`${title}\\n${text}\\n${url}`);\n        setShowToast(true);\n        setTimeout(() => setShowToast(false), 2000);\n      } catch (clipboardError) {\n        console.error('Error copying to clipboard:', clipboardError);\n      }\n    }\n  };\n\n  return (\n    <>\n      <button\n        onClick={handleShare}\n        className=\"p-2 border-2 border-black dark:border-white text-black dark:text-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200\"\n        aria-label=\"分享故事\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\" />\n        </svg>\n      </button>\n\n      {/* Toast notification */}\n      {showToast && (\n        <div className=\"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-black dark:bg-white text-white dark:text-black px-6 py-3 font-bold uppercase tracking-wide text-sm shadow-lg z-50 animate-in slide-in-from-bottom duration-200\">\n          已复制到剪贴板\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,OAAO,QAAQ,CAAC,IAAI,EAAoB;;IAC/F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAAc;QAClB,MAAM,YAAY;YAChB;YACA;YACA;QACF;QAEA,IAAI;YACF,IAAI,UAAU,KAAK,EAAE;gBACnB,MAAM,UAAU,KAAK,CAAC;YACxB,OAAO;gBACL,8BAA8B;gBAC9B,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK;gBAC/D,aAAa;gBACb,WAAW,IAAM,aAAa,QAAQ;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,8BAA8B;YAC9B,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK;gBAC/D,aAAa;gBACb,WAAW,IAAM,aAAa,QAAQ;YACxC,EAAE,OAAO,gBAAgB;gBACvB,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;IACF;IAEA,qBACE;;0BACE,6LAAC;gBACC,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;YAKxE,2BACC,6LAAC;gBAAI,WAAU;0BAAuN;;;;;;;;AAM9O;GApDwB;KAAA", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/StoryModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport ShareButton from './ShareButton';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\ninterface StoryModalProps {\n  story: CatStory | null;\n  onClose: () => void;\n}\n\nexport default function StoryModal({ story, onClose }: StoryModalProps) {\n  useEffect(() => {\n    if (story) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [story]);\n\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (story) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [story, onClose]);\n\n  if (!story) return null;\n\n  return (\n    <div\n      className=\"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200\"\n      onClick={onClose}\n    >\n      <div\n        className=\"bg-white dark:bg-gray-900 max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-in zoom-in-95 duration-200 border-4 border-black dark:border-white\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Magazine Article Header */}\n        <header className=\"border-b-2 border-black dark:border-white p-8\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"flex-1\">\n              <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2\">\n                特别故事\n              </div>\n              <h1 className=\"text-3xl md:text-4xl lg:text-5xl font-black text-black dark:text-white mb-4 leading-tight\">\n                {story.title}\n              </h1>\n              <div className=\"text-center mb-4\">\n                <div className=\"text-6xl\">{story.emoji}</div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-2 ml-4\">\n              <ShareButton\n                title={`${story.title}`}\n                text={story.content.substring(0, 100) + '...'}\n              />\n              <button\n                onClick={onClose}\n                className=\"text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 text-2xl font-bold w-10 h-10 flex items-center justify-center transition-all duration-200\"\n                aria-label=\"关闭故事\"\n              >\n                ×\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Magazine Article Content */}\n        <article className=\"p-8\">\n          <div className=\"prose prose-lg max-w-none\">\n            <p className=\"text-gray-800 dark:text-gray-200 leading-relaxed text-lg font-serif first-letter:text-6xl first-letter:font-black first-letter:text-black dark:first-letter:text-white first-letter:float-left first-letter:mr-3 first-letter:mt-2 first-letter:leading-none\">\n              {story.content}\n            </p>\n          </div>\n\n          {/* Article End */}\n          <div className=\"mt-12 pt-8 border-t border-gray-300 dark:border-gray-600 text-center\">\n            <div className=\"inline-block bg-black dark:bg-white text-white dark:text-black px-6 py-2 font-bold uppercase tracking-wide text-sm mb-4\">\n              故事完\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400 text-sm italic\">\n              \"每一个故事都是一段温暖的旅程\"\n            </p>\n          </div>\n        </article>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAkBe,SAAS,WAAW,EAAE,KAAK,EAAE,OAAO,EAAmB;;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,OAAO;gBACT,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;wCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;+BAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,OAAO;gBACT,SAAS,gBAAgB,CAAC,WAAW;YACvC;YAEA;wCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;+BAAG;QAAC;QAAO;KAAQ;IAEnB,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,6LAAC;YACC,WAAU;YACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8BAGjC,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoF;;;;;;kDAGnG,6LAAC;wCAAG,WAAU;kDACX,MAAM,KAAK;;;;;;kDAEd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAY,MAAM,KAAK;;;;;;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAW;wCACV,OAAO,GAAG,MAAM,KAAK,EAAE;wCACvB,MAAM,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;;;;;;kDAE1C,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDACZ;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAQ,WAAU;;sCACjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,MAAM,OAAO;;;;;;;;;;;sCAKlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA0H;;;;;;8CAGzI,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E;GA5FwB;KAAA", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void;\n  placeholder?: string;\n}\n\nexport default function SearchBar({ onSearch, placeholder = \"搜索猫咪故事...\" }: SearchBarProps) {\n  const [query, setQuery] = useState('');\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSearch(query);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setQuery(value);\n    onSearch(value); // Real-time search\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <div className=\"text-center mb-4\">\n        <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400\">\n          搜索故事\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"relative\">\n        <input\n          type=\"text\"\n          value={query}\n          onChange={handleChange}\n          placeholder={placeholder}\n          className=\"w-full px-6 py-4 pl-14 pr-14 text-lg text-black dark:text-white bg-white dark:bg-gray-800 border-2 border-black dark:border-white focus:outline-none focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600 transition-all duration-200 font-medium\"\n        />\n\n        <div className=\"absolute left-4 top-1/2 transform -translate-y-1/2\">\n          <svg\n            className=\"w-6 h-6 text-black dark:text-white\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            />\n          </svg>\n        </div>\n\n        {query && (\n          <button\n            type=\"button\"\n            onClick={() => {\n              setQuery('');\n              onSearch('');\n            }}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 p-1 transition-colors duration-200\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        )}\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,WAAW,EAAkB;;IACvF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,SAAS,QAAQ,mBAAmB;IACtC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAA+E;;;;;;;;;;;0BAKhG,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;oBAKP,uBACC,6LAAC;wBACC,MAAK;wBACL,SAAS;4BACP,SAAS;4BACT,SAAS;wBACX;wBACA,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;GAhEwB;KAAA", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [isDark, setIsDark] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDark;\n    setIsDark(newTheme);\n    \n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className=\"fixed top-4 right-4 z-50 p-3 bg-black dark:bg-white text-white dark:text-black border-2 border-black dark:border-white hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 font-bold\"\n      aria-label={isDark ? '切换到亮色模式' : '切换到暗色模式'}\n    >\n      <span className=\"text-lg\">\n        {isDark ? '☀️' : '🌙'}\n      </span>\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,4DAA4D;YAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAE7E,IAAI,eAAe,UAAW,CAAC,cAAc,aAAc;gBACzD,UAAU;gBACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC;QACF;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,CAAC;QAClB,UAAU;QAEV,IAAI,UAAU;YACZ,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,SAAS,YAAY;kBAEjC,cAAA,6LAAC;YAAK,WAAU;sBACb,SAAS,OAAO;;;;;;;;;;;AAIzB;GAtCwB;KAAA", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/components/TableOfContents.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\ninterface TableOfContentsProps {\n  stories: CatStory[];\n  onStorySelect: (story: CatStory) => void;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function TableOfContents({ stories, onStorySelect, isOpen, onClose }: TableOfContentsProps) {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200\">\n      <div className=\"bg-white dark:bg-gray-900 max-w-4xl w-full max-h-[90vh] overflow-y-auto border-4 border-black dark:border-white\">\n        {/* Header */}\n        <header className=\"border-b-2 border-black dark:border-white p-8\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2\">\n                本期目录\n              </div>\n              <h2 className=\"text-3xl md:text-4xl font-black text-black dark:text-white\">\n                CAT STORIES\n              </h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n                第1期 • 2024年冬季刊 • 共{stories.length}篇故事\n              </p>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 text-2xl font-bold w-10 h-10 flex items-center justify-center transition-all duration-200\"\n            >\n              ×\n            </button>\n          </div>\n        </header>\n\n        {/* Contents */}\n        <div className=\"p-8\">\n          <div className=\"grid gap-4\">\n            {stories.map((story, index) => (\n              <article\n                key={story.id}\n                className=\"border-b border-gray-300 dark:border-gray-600 pb-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-4 transition-colors duration-200\"\n                onClick={() => {\n                  onStorySelect(story);\n                  onClose();\n                }}\n              >\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"text-2xl font-black text-gray-400 dark:text-gray-500 w-8\">\n                    {String(index + 1).padStart(2, '0')}\n                  </div>\n                  <div className=\"text-3xl\">\n                    {story.emoji}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-xl font-bold text-black dark:text-white mb-1\">\n                      {story.title}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400 line-clamp-2\">\n                      {story.content.substring(0, 100)}...\n                    </p>\n                  </div>\n                  <div className=\"text-xs font-bold uppercase tracking-wide text-gray-500 dark:text-gray-400\">\n                    阅读 →\n                  </div>\n                </div>\n              </article>\n            ))}\n          </div>\n\n          {/* Footer */}\n          <div className=\"mt-8 pt-8 border-t border-gray-300 dark:border-gray-600 text-center\">\n            <p className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400\">\n              点击任意故事开始阅读\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAmBe,SAAS,gBAAgB,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvG,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAoF;;;;;;kDAGnG,6LAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,6LAAC;wCAAE,WAAU;;4CAAgD;4CACxC,QAAQ,MAAM;4CAAC;;;;;;;;;;;;;0CAGtC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,6LAAC;oCAEC,WAAU;oCACV,SAAS;wCACP,cAAc;wCACd;oCACF;8CAEA,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;0DAEjC,6LAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK;;;;;;0DAEd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAE,WAAU;;4DACV,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;4DAAK;;;;;;;;;;;;;0DAGrC,6LAAC;gDAAI,WAAU;0DAA6E;;;;;;;;;;;;mCAtBzF,MAAM,EAAE;;;;;;;;;;sCA+BnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA+E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG;KA1EwB", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/hooks/useFavorites.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useFavorites() {\n  const [favorites, setFavorites] = useState<number[]>([]);\n\n  // Load favorites from localStorage on mount\n  useEffect(() => {\n    const saved = localStorage.getItem('cat-stories-favorites');\n    if (saved) {\n      try {\n        setFavorites(JSON.parse(saved));\n      } catch (error) {\n        console.error('Error loading favorites:', error);\n      }\n    }\n  }, []);\n\n  // Save favorites to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cat-stories-favorites', JSON.stringify(favorites));\n  }, [favorites]);\n\n  const addToFavorites = (storyId: number) => {\n    setFavorites(prev => [...prev, storyId]);\n  };\n\n  const removeFromFavorites = (storyId: number) => {\n    setFavorites(prev => prev.filter(id => id !== storyId));\n  };\n\n  const toggleFavorite = (storyId: number) => {\n    if (favorites.includes(storyId)) {\n      removeFromFavorites(storyId);\n    } else {\n      addToFavorites(storyId);\n    }\n  };\n\n  const isFavorite = (storyId: number) => {\n    return favorites.includes(storyId);\n  };\n\n  return {\n    favorites,\n    addToFavorites,\n    removeFromFavorites,\n    toggleFavorite,\n    isFavorite\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,IAAI;oBACF,aAAa,KAAK,KAAK,CAAC;gBAC1B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC5C;YACF;QACF;iCAAG,EAAE;IAEL,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;QAC/D;iCAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IACzC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,QAAQ,CAAC,UAAU;YAC/B,oBAAoB;QACtB,OAAO;YACL,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,UAAU,QAAQ,CAAC;IAC5B;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GA/CgB", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport StoryCard from '@/components/StoryCard';\nimport StoryModal from '@/components/StoryModal';\nimport CatIcon from '@/components/CatIcon';\nimport SearchBar from '@/components/SearchBar';\nimport ThemeToggle from '@/components/ThemeToggle';\nimport TableOfContents from '@/components/TableOfContents';\nimport { useFavorites } from '@/hooks/useFavorites';\n\ninterface CatStory {\n  id: number;\n  title: string;\n  content: string;\n  emoji: string;\n  color: string;\n}\n\nconst catStories: CatStory[] = [\n  {\n    id: 1,\n    title: \"小橘猫的冒险\",\n    content: \"从前有一只可爱的小橘猫叫做咪咪，它住在一个温馨的小屋里。每天早上，咪咪都会在阳光下伸懒腰，然后开始它的日常冒险。今天，咪咪发现了花园里有一只美丽的蝴蝶，它决定跟着蝴蝶去探索未知的世界...\",\n    emoji: \"🐱\",\n    color: \"bg-gradient-to-br from-orange-200 to-orange-300\"\n  },\n  {\n    id: 2,\n    title: \"月光下的白猫\",\n    content: \"在一个宁静的夜晚，一只雪白的猫咪悄悄地走在月光下。它的毛发在月光的照耀下闪闪发光，就像天使一样美丽。白猫喜欢在夜晚漫步，因为这时候世界特别安静，它可以听到自己内心的声音...\",\n    emoji: \"🌙\",\n    color: \"bg-gradient-to-br from-blue-200 to-purple-300\"\n  },\n  {\n    id: 3,\n    title: \"花园里的小黑猫\",\n    content: \"小黑猫露娜最喜欢在花园里玩耍。它会在玫瑰花丛中捉迷藏，在薰衣草田里打滚，还会和蜜蜂做朋友。虽然其他动物有时候会害怕它的黑色毛发，但露娜其实是最温柔善良的猫咪...\",\n    emoji: \"🌸\",\n    color: \"bg-gradient-to-br from-pink-200 to-rose-300\"\n  },\n  {\n    id: 4,\n    title: \"爱睡觉的灰猫\",\n    content: \"灰猫咕咕是世界上最爱睡觉的猫咪。它可以在任何地方睡着：阳光下的窗台、柔软的沙发、甚至是主人的键盘上。咕咕的梦境总是充满了鱼和毛线球，每次醒来都会满足地打个哈欠...\",\n    emoji: \"😴\",\n    color: \"bg-gradient-to-br from-gray-200 to-slate-300\"\n  },\n  {\n    id: 5,\n    title: \"勇敢的虎斑猫\",\n    content: \"虎斑猫托尼是社区里最勇敢的猫咪。当其他小动物遇到困难时，托尼总是第一个站出来帮助。它曾经救过掉进水池的小鸟，也帮助过迷路的小老鼠找到回家的路。大家都很敬佩托尼的勇气和善良...\",\n    emoji: \"🦸\",\n    color: \"bg-gradient-to-br from-yellow-200 to-amber-300\"\n  },\n  {\n    id: 6,\n    title: \"爱吃鱼的小花猫\",\n    content: \"小花猫咪咪最大的爱好就是吃鱼。它知道镇上每一家鱼店的开门时间，也认识所有卖鱼的叔叔阿姨。咪咪特别聪明，它学会了用可爱的眼神和甜美的叫声来获得美味的鱼儿。每天的鱼餐时间是咪咪最快乐的时光...\",\n    emoji: \"🐟\",\n    color: \"bg-gradient-to-br from-teal-200 to-cyan-300\"\n  },\n  {\n    id: 7,\n    title: \"会弹钢琴的音乐猫\",\n    content: \"在一个音乐世家里住着一只特别的猫咪叫做莫扎特。它从小就对音乐有着天生的敏感，经常在主人练琴时静静地坐在旁边聆听。有一天，莫扎特竟然学会了用爪子弹奏简单的旋律，成为了世界上第一只会弹钢琴的猫咪...\",\n    emoji: \"🎹\",\n    color: \"bg-gradient-to-br from-indigo-200 to-blue-300\"\n  },\n  {\n    id: 8,\n    title: \"爱画画的艺术猫\",\n    content: \"小艺是一只充满创意的猫咪，它最喜欢用爪子蘸颜料在画布上创作。它的作品色彩斑斓，充满了猫咪独特的视角和想象力。小艺的画作在当地艺术展上大受欢迎，人们都惊叹于这只猫咪的艺术天赋...\",\n    emoji: \"🎨\",\n    color: \"bg-gradient-to-br from-red-200 to-pink-300\"\n  },\n  {\n    id: 9,\n    title: \"爱读书的学者猫\",\n    content: \"图书馆里住着一只博学的猫咪叫做牛顿。它每天都会坐在书架旁，似乎在认真地'阅读'各种书籍。虽然猫咪不会真的读书，但牛顿对知识的渴望和对学习的热爱感染了每一个来图书馆的人...\",\n    emoji: \"📚\",\n    color: \"bg-gradient-to-br from-green-200 to-emerald-300\"\n  }\n];\n\nexport default function Home() {\n  const [selectedStory, setSelectedStory] = useState<CatStory | null>(null);\n  const [currentPage, setCurrentPage] = useState(0);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);\n  const [showTableOfContents, setShowTableOfContents] = useState(false);\n  const { toggleFavorite, isFavorite, favorites } = useFavorites();\n  const storiesPerPage = 3;\n\n  // Filter stories based on search query and favorites\n  const filteredStories = useMemo(() => {\n    let stories = catStories;\n\n    // Filter by favorites if enabled\n    if (showFavoritesOnly) {\n      stories = stories.filter(story => favorites.includes(story.id));\n    }\n\n    // Filter by search query\n    if (searchQuery.trim()) {\n      stories = stories.filter(story =>\n        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        story.content.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    return stories;\n  }, [searchQuery, showFavoritesOnly, favorites]);\n\n  const totalPages = Math.ceil(filteredStories.length / storiesPerPage);\n  const currentStories = filteredStories.slice(\n    currentPage * storiesPerPage,\n    (currentPage + 1) * storiesPerPage\n  );\n\n  // Reset to first page when search changes\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    setCurrentPage(0);\n  };\n\n  // Toggle favorites filter\n  const handleToggleFavoritesFilter = () => {\n    setShowFavoritesOnly(!showFavoritesOnly);\n    setCurrentPage(0);\n  };\n\n  // Random story function\n  const handleRandomStory = () => {\n    const randomIndex = Math.floor(Math.random() * catStories.length);\n    setSelectedStory(catStories[randomIndex]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\">\n      {/* Theme Toggle */}\n      <ThemeToggle />\n\n      {/* Magazine Header */}\n      <header className=\"border-b-4 border-black dark:border-white bg-white dark:bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto px-4 py-6\">\n          {/* Magazine Title */}\n          <div className=\"text-center mb-6\">\n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-black uppercase tracking-tight text-black dark:text-white mb-2\">\n              CAT STORIES\n            </h1>\n            <div className=\"flex items-center justify-center gap-4 text-sm font-bold uppercase tracking-widest text-gray-600 dark:text-gray-400\">\n              <span>第1期</span>\n              <span>•</span>\n              <span>2024年冬季刊</span>\n              <span>•</span>\n              <span>温馨猫咪专辑</span>\n            </div>\n          </div>\n\n          {/* Magazine Subtitle */}\n          <div className=\"text-center border-t border-b border-gray-300 dark:border-gray-600 py-4 mb-6\">\n            <p className=\"text-lg md:text-xl font-serif italic text-gray-700 dark:text-gray-300\">\n              \"每一个故事都是一段温暖的旅程\"\n            </p>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <button\n              onClick={() => setShowTableOfContents(true)}\n              className=\"w-full sm:w-auto border-2 border-black dark:border-white text-black dark:text-white px-8 py-3 font-bold uppercase tracking-wide hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200\"\n            >\n              目录\n            </button>\n\n            <button\n              onClick={handleRandomStory}\n              className=\"w-full sm:w-auto bg-black dark:bg-white text-white dark:text-black px-8 py-3 font-bold uppercase tracking-wide hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n            >\n              随机阅读\n            </button>\n\n            <button\n              onClick={handleToggleFavoritesFilter}\n              className={`w-full sm:w-auto px-8 py-3 font-bold uppercase tracking-wide transition-all duration-200 ${\n                showFavoritesOnly\n                  ? 'bg-red-600 text-white hover:bg-red-700'\n                  : 'border-2 border-black dark:border-white text-black dark:text-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black'\n              }`}\n            >\n              {showFavoritesOnly ? '显示全部' : '我的收藏'} ({favorites.length})\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Story Modal */}\n      <StoryModal\n        story={selectedStory}\n        onClose={() => setSelectedStory(null)}\n      />\n\n      {/* Table of Contents */}\n      <TableOfContents\n        stories={catStories}\n        onStorySelect={setSelectedStory}\n        isOpen={showTableOfContents}\n        onClose={() => setShowTableOfContents(false)}\n      />\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 py-8\">\n        {/* Search Bar */}\n        <div className=\"mb-8\">\n          <SearchBar onSearch={handleSearch} />\n        </div>\n\n        {/* Search Results Info */}\n        {searchQuery && (\n          <div className=\"text-center mb-8 px-4\">\n            <div className=\"inline-block bg-black dark:bg-white text-white dark:text-black px-4 py-2 font-bold uppercase tracking-wide text-sm\">\n              搜索结果: {filteredStories.length} 篇关于 \"{searchQuery}\" 的故事\n            </div>\n          </div>\n        )}\n\n        {/* Magazine Layout */}\n        {currentStories.length > 0 ? (\n          <div className=\"space-y-12\">\n            {/* Featured Story (First Story) */}\n            {currentPage === 0 && currentStories.length > 0 && (\n              <article className=\"border-b-2 border-black dark:border-white pb-12 mb-12\">\n                <div className=\"grid lg:grid-cols-2 gap-8 items-center\">\n                  <div>\n                    <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2\">\n                      本期特稿\n                    </div>\n                    <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-black mb-4 text-black dark:text-white leading-tight\">\n                      {currentStories[0].title}\n                    </h2>\n                    <p className=\"text-lg text-gray-700 dark:text-gray-300 mb-6 leading-relaxed\">\n                      {currentStories[0].content.substring(0, 200)}...\n                    </p>\n                    <div className=\"flex items-center gap-4\">\n                      <button\n                        onClick={() => setSelectedStory(currentStories[0])}\n                        className=\"bg-black dark:bg-white text-white dark:text-black px-6 py-3 font-bold uppercase tracking-wide hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n                      >\n                        阅读全文\n                      </button>\n                      <button\n                        onClick={() => toggleFavorite(currentStories[0].id)}\n                        className=\"p-3 border-2 border-black dark:border-white hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-all duration-200\"\n                      >\n                        {isFavorite(currentStories[0].id) ? '❤️' : '🤍'}\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-8xl md:text-9xl mb-4\">\n                      {currentStories[0].emoji}\n                    </div>\n                  </div>\n                </div>\n              </article>\n            )}\n\n            {/* Other Stories Grid */}\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {currentStories.slice(currentPage === 0 ? 1 : 0).map((story, index) => (\n                <article key={story.id} className=\"border-b border-gray-300 dark:border-gray-600 pb-6\">\n                  <div className=\"text-center mb-4\">\n                    <div className=\"text-4xl mb-2\">{story.emoji}</div>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-black dark:text-white\">\n                    {story.title}\n                  </h3>\n                  <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed\">\n                    {story.content.substring(0, 120)}...\n                  </p>\n                  <div className=\"flex items-center justify-between\">\n                    <button\n                      onClick={() => setSelectedStory(story)}\n                      className=\"text-xs font-bold uppercase tracking-wide text-black dark:text-white hover:underline\"\n                    >\n                      阅读更多 →\n                    </button>\n                    <button\n                      onClick={() => toggleFavorite(story.id)}\n                      className=\"text-lg hover:scale-110 transition-transform duration-200\"\n                    >\n                      {isFavorite(story.id) ? '❤️' : '🤍'}\n                    </button>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center py-16 px-4\">\n            <div className=\"text-6xl mb-6\">📰</div>\n            <h3 className=\"text-2xl font-bold text-black dark:text-white mb-4\">\n              {showFavoritesOnly ? '收藏夹空空如也' : '未找到相关内容'}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {showFavoritesOnly ? '开始收藏您喜欢的故事吧！' : '尝试其他搜索关键词'}\n            </p>\n          </div>\n        )}\n\n        {/* Magazine Pagination */}\n        {totalPages > 1 && (\n          <div className=\"mt-12 pt-8 border-t-2 border-black dark:border-white\">\n            <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n              <button\n                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n                disabled={currentPage === 0}\n                className=\"w-full sm:w-auto px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-bold uppercase tracking-wide disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n              >\n                ← 上一页\n              </button>\n\n              <div className=\"text-center\">\n                <div className=\"text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-1\">\n                  页码\n                </div>\n                <div className=\"text-2xl font-black text-black dark:text-white\">\n                  {currentPage + 1} / {totalPages}\n                </div>\n              </div>\n\n              <button\n                onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}\n                disabled={currentPage === totalPages - 1}\n                className=\"w-full sm:w-auto px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-bold uppercase tracking-wide disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200\"\n              >\n                下一页 →\n              </button>\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Magazine Footer */}\n      <footer className=\"bg-black dark:bg-white text-white dark:text-black py-12 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"grid md:grid-cols-3 gap-8 text-center md:text-left\">\n            <div>\n              <h3 className=\"text-xl font-black uppercase tracking-wide mb-4\">编辑部</h3>\n              <p className=\"text-sm opacity-80\">\n                专注于分享温馨可爱的猫咪故事<br />\n                每一个故事都经过精心编写\n              </p>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-black uppercase tracking-wide mb-4\">本期内容</h3>\n              <p className=\"text-sm opacity-80\">\n                {catStories.length} 篇精选故事<br />\n                涵盖各种可爱猫咪角色\n              </p>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-black uppercase tracking-wide mb-4\">联系我们</h3>\n              <p className=\"text-sm opacity-80\">\n                感谢您的阅读与支持<br />\n                期待与您分享更多美好故事\n              </p>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-600 dark:border-gray-400 mt-8 pt-8 text-center\">\n            <p className=\"text-xs font-bold uppercase tracking-widest opacity-60\">\n              CAT STORIES MAGAZINE © 2024 • 温馨猫咪故事专刊\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;AAmBA,MAAM,aAAyB;IAC7B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC7D,MAAM,iBAAiB;IAEvB,qDAAqD;IACrD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YAC9B,IAAI,UAAU;YAEd,iCAAiC;YACjC,IAAI,mBAAmB;gBACrB,UAAU,QAAQ,MAAM;qDAAC,CAAA,QAAS,UAAU,QAAQ,CAAC,MAAM,EAAE;;YAC/D;YAEA,yBAAyB;YACzB,IAAI,YAAY,IAAI,IAAI;gBACtB,UAAU,QAAQ,MAAM;qDAAC,CAAA,QACvB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;YAEhE;YAEA,OAAO;QACT;wCAAG;QAAC;QAAa;QAAmB;KAAU;IAE9C,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG;IACtD,MAAM,iBAAiB,gBAAgB,KAAK,CAC1C,cAAc,gBACd,CAAC,cAAc,CAAC,IAAI;IAGtB,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe;IACjB;IAEA,0BAA0B;IAC1B,MAAM,8BAA8B;QAClC,qBAAqB,CAAC;QACtB,eAAe;IACjB;IAEA,wBAAwB;IACxB,MAAM,oBAAoB;QACxB,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;QAChE,iBAAiB,UAAU,CAAC,YAAY;IAC1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,oIAAA,CAAA,UAAW;;;;;0BAGZ,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuG;;;;;;8CAGrH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwE;;;;;;;;;;;sCAMvF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CACX;;;;;;8CAID,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAID,6LAAC;oCACC,SAAS;oCACT,WAAW,CAAC,yFAAyF,EACnG,oBACI,2CACA,gJACJ;;wCAED,oBAAoB,SAAS;wCAAO;wCAAG,UAAU,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAOjE,6LAAC,mIAAA,CAAA,UAAU;gBACT,OAAO;gBACP,SAAS,IAAM,iBAAiB;;;;;;0BAIlC,6LAAC,wIAAA,CAAA,UAAe;gBACd,SAAS;gBACT,eAAe;gBACf,QAAQ;gBACR,SAAS,IAAM,uBAAuB;;;;;;0BAIxC,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;4BAAC,UAAU;;;;;;;;;;;oBAItB,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCAAqH;gCAC3H,gBAAgB,MAAM;gCAAC;gCAAO;gCAAY;;;;;;;;;;;;oBAMtD,eAAe,MAAM,GAAG,kBACvB,6LAAC;wBAAI,WAAU;;4BAEZ,gBAAgB,KAAK,eAAe,MAAM,GAAG,mBAC5C,6LAAC;gCAAQ,WAAU;0CACjB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAoF;;;;;;8DAGnG,6LAAC;oDAAG,WAAU;8DACX,cAAc,CAAC,EAAE,CAAC,KAAK;;;;;;8DAE1B,6LAAC;oDAAE,WAAU;;wDACV,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG;wDAAK;;;;;;;8DAE/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,iBAAiB,cAAc,CAAC,EAAE;4DACjD,WAAU;sEACX;;;;;;sEAGD,6LAAC;4DACC,SAAS,IAAM,eAAe,cAAc,CAAC,EAAE,CAAC,EAAE;4DAClD,WAAU;sEAET,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO;;;;;;;;;;;;;;;;;;sDAIjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,cAAc,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAQlC,6LAAC;gCAAI,WAAU;0CACZ,eAAe,KAAK,CAAC,gBAAgB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC3D,6LAAC;wCAAuB,WAAU;;0DAChC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAAiB,MAAM,KAAK;;;;;;;;;;;0DAE7C,6LAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAEd,6LAAC;gDAAE,WAAU;;oDACV,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;oDAAK;;;;;;;0DAEnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,iBAAiB;wDAChC,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,eAAe,MAAM,EAAE;wDACtC,WAAU;kEAET,WAAW,MAAM,EAAE,IAAI,OAAO;;;;;;;;;;;;;uCArBvB,MAAM,EAAE;;;;;;;;;;;;;;;6CA6B5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CACX,oBAAoB,YAAY;;;;;;0CAEnC,6LAAC;gCAAE,WAAU;0CACV,oBAAoB,iBAAiB;;;;;;;;;;;;oBAM3C,aAAa,mBACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;oCACxD,UAAU,gBAAgB;oCAC1B,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAoF;;;;;;sDAGnG,6LAAC;4CAAI,WAAU;;gDACZ,cAAc;gDAAE;gDAAI;;;;;;;;;;;;;8CAIzB,6LAAC;oCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;oCACrE,UAAU,gBAAgB,aAAa;oCACvC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,6LAAC;4CAAE,WAAU;;gDAAqB;8DAClB,6LAAC;;;;;gDAAK;;;;;;;;;;;;;8CAIxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,6LAAC;4CAAE,WAAU;;gDACV,WAAW,MAAM;gDAAC;8DAAM,6LAAC;;;;;gDAAK;;;;;;;;;;;;;8CAInC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,6LAAC;4CAAE,WAAU;;gDAAqB;8DACvB,6LAAC;;;;;gDAAK;;;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlF;GAvSwB;;QAM4B,+HAAA,CAAA,eAAY;;;KANxC", "debugId": null}}, {"offset": {"line": 1562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AAAAAA/cat-stories/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}