'use client';

import { useState, useEffect } from 'react';

export function useFavorites() {
  const [favorites, setFavorites] = useState<number[]>([]);

  // Load favorites from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('cat-stories-favorites');
    if (saved) {
      try {
        setFavorites(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading favorites:', error);
      }
    }
  }, []);

  // Save favorites to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cat-stories-favorites', JSON.stringify(favorites));
  }, [favorites]);

  const addToFavorites = (storyId: number) => {
    setFavorites(prev => [...prev, storyId]);
  };

  const removeFromFavorites = (storyId: number) => {
    setFavorites(prev => prev.filter(id => id !== storyId));
  };

  const toggleFavorite = (storyId: number) => {
    if (favorites.includes(storyId)) {
      removeFromFavorites(storyId);
    } else {
      addToFavorites(storyId);
    }
  };

  const isFavorite = (storyId: number) => {
    return favorites.includes(storyId);
  };

  return {
    favorites,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite
  };
}
