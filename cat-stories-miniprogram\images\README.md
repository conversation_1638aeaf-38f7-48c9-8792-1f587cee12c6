# 🖼️ 图片资源目录

这个目录用于存放小程序所需的图片资源。

## 📱 TabBar 图标 (必需)

### 已创建的SVG图标文件
- ✅ `home.svg` - 首页图标 (未选中状态)
- ✅ `home-active.svg` - 首页图标 (选中状态)
- ✅ `list.svg` - 目录图标 (未选中状态)
- ✅ `list-active.svg` - 目录图标 (选中状态)
- ✅ `cover.svg` - 封面图标 (未选中状态)
- ✅ `cover-active.svg` - 封面图标 (选中状态)

### 🔄 转换为PNG格式

**方法1：使用转换工具页面**
1. 在浏览器中打开 `convert-icons.html` 文件
2. 点击对应按钮下载PNG格式图标
3. 将下载的PNG文件放入此目录

**方法2：在线转换工具**
1. 访问以下任一在线转换网站：
   - [Convertio SVG转PNG](https://convertio.co/zh/svg-png/)
   - [AConvert SVG转PNG](https://www.aconvert.com/cn/image/svg-to-png/)
   - [CloudConvert SVG转PNG](https://cloudconvert.com/svg-to-png)
2. 上传SVG文件，设置输出尺寸为 **81×81 像素**
3. 下载转换后的PNG文件

**方法3：使用设计软件**
- Adobe Illustrator、Sketch、Figma等都可以导出PNG
- 确保导出尺寸为81×81像素，背景透明

### 需要的PNG文件
转换完成后，确保以下PNG文件存在：
- `home.png` - 首页图标 (未选中状态)
- `home-active.png` - 首页图标 (选中状态)
- `list.png` - 目录图标 (未选中状态)
- `list-active.png` - 目录图标 (选中状态)
- `cover.png` - 封面图标 (未选中状态)
- `cover-active.png` - 封面图标 (选中状态)

## 🎨 故事插画 (可选)

如果要使用真实的 Eric Carle 风格插画，可以将图片放在以下位置：
- `story-1.jpg` - 小橘猫的冒险
- `story-2.jpg` - 月光下的白猫
- `story-3.jpg` - 花园里的小黑猫
- `story-4.jpg` - 爱睡觉的灰猫
- `story-5.jpg` - 勇敢的虎斑猫
- `story-6.jpg` - 小花猫的鱼儿梦
- `story-7.jpg` - 音乐猫的钢琴梦
- `story-8.jpg` - 艺术家小猫
- `story-9.jpg` - 学者猫的图书馆

## 📏 图片规格要求

### TabBar 图标
- **尺寸：** 81px × 81px (微信小程序标准)
- **格式：** PNG
- **背景：** 透明
- **颜色：** 未选中状态使用 #666666，选中状态使用 #000000

### 故事插画
- **尺寸：** 建议 800px × 600px 或更高
- **格式：** JPG 或 PNG
- **风格：** Eric Carle 拼贴画风格
- **文件大小：** 建议每张图片不超过 200KB

## ⚠️ 重要注意事项

1. **TabBar图标是必需的** - 没有PNG图标，底部导航将无法正常显示
2. **文件命名严格** - 必须使用确切的文件名，区分大小写
3. **图片优化** - 所有图片都应该经过压缩优化，减少小程序包大小
4. **故事插画可选** - 如果不提供真实插画，应用会使用表情符号作为占位符
5. **测试验证** - 添加图标后，在微信开发者工具中预览确认显示正常

## 🚀 快速开始

1. **立即转换图标：** 打开 `convert-icons.html` 获取PNG图标
2. **放置文件：** 将6个PNG文件放入此目录
3. **预览测试：** 在微信开发者工具中查看TabBar效果
4. **可选添加：** 根据需要添加故事插画图片
