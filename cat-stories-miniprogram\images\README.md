# 图片资源目录

这个目录用于存放小程序所需的图片资源。

## 需要的图片文件

### TabBar 图标
- `home.png` - 首页图标 (未选中状态)
- `home-active.png` - 首页图标 (选中状态)
- `list.png` - 目录图标 (未选中状态)
- `list-active.png` - 目录图标 (选中状态)
- `cover.png` - 封面图标 (未选中状态)
- `cover-active.png` - 封面图标 (选中状态)

### 故事插画 (可选)
如果要使用真实的 Eric Carle 风格插画，可以将图片放在以下位置：
- `story-1.jpg` - 小橘猫的冒险
- `story-2.jpg` - 月光下的白猫
- `story-3.jpg` - 花园里的小黑猫
- `story-4.jpg` - 爱睡觉的灰猫
- `story-5.jpg` - 勇敢的虎斑猫
- `story-6.jpg` - 小花猫的鱼儿梦
- `story-7.jpg` - 音乐猫的钢琴梦
- `story-8.jpg` - 艺术家小猫
- `story-9.jpg` - 学者猫的图书馆

## 图片规格建议

### TabBar 图标
- 尺寸：81px × 81px
- 格式：PNG
- 背景：透明

### 故事插画
- 尺寸：建议 800px × 600px 或更高
- 格式：JPG 或 PNG
- 风格：Eric Carle 拼贴画风格

## 注意事项

1. 所有图片都应该经过压缩优化，以减少小程序包大小
2. 图片文件名应该使用小写字母和连字符
3. 如果不提供真实插画，应用会使用表情符号作为占位符
4. TabBar 图标是必需的，否则底部导航可能显示异常
