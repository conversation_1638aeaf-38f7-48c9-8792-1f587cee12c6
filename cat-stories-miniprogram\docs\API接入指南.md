# 猫咪故事杂志 - API接入指南

## 📋 概述

本文档详细说明了如何将猫咪故事杂志小程序接入后端API，实现数据的云端同步和管理。

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────┐
│   页面层 (Pages) │  ← 用户界面和交互
├─────────────────┤
│  服务层 (Services) │  ← 业务逻辑封装
├─────────────────┤
│ 数据管理层 (DataManager) │  ← 本地/远程数据管理
├─────────────────┤
│  网络层 (API Utils) │  ← HTTP请求封装
├─────────────────┤
│   后端API       │  ← 服务器接口
└─────────────────┘
```

### 核心组件

1. **API Service** (`utils/api.js`) - 网络请求封装
2. **Story Service** (`services/storyService.js`) - 故事相关API
3. **User Service** (`services/userService.js`) - 用户相关API
4. **Data Manager** (`utils/dataManager.js`) - 数据管理器
5. **API Config** (`config/api.config.js`) - 配置管理

## 🔧 快速开始

### 1. 环境配置

在 `config/api.config.js` 中配置API地址：

```javascript
const ENV_CONFIG = {
  development: {
    baseURL: 'https://your-dev-api.com',
    timeout: 10000
  },
  production: {
    baseURL: 'https://your-api.com',
    timeout: 15000
  }
};
```

### 2. 初始化

在 `app.js` 中初始化数据管理器：

```javascript
import dataManager from './utils/dataManager.js';

App({
  async onLaunch() {
    // 初始化数据管理器
    await dataManager.init();
  }
});
```

### 3. 使用API

在页面中使用API服务：

```javascript
import dataManager from '../../utils/dataManager.js';

Page({
  async loadStories() {
    const result = await dataManager.getStories({
      page: 1,
      pageSize: 10
    });
    
    if (result.success) {
      this.setData({
        stories: result.data
      });
    }
  }
});
```

## 📡 API接口规范

### 请求格式

所有API请求都应该遵循以下格式：

```http
POST /api/endpoint
Content-Type: application/json
Authorization: Bearer <token>

{
  "param1": "value1",
  "param2": "value2"
}
```

### 响应格式

统一的响应格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": 1640995200000
}
```

### 错误响应

```json
{
  "code": 1001,
  "message": "认证失败",
  "data": null,
  "timestamp": 1640995200000
}
```

## 🔐 认证系统

### 微信登录流程

```mermaid
sequenceDiagram
    participant MP as 小程序
    participant WX as 微信服务器
    participant API as 后端API
    
    MP->>WX: wx.login()
    WX->>MP: 返回code
    MP->>API: POST /api/auth/wx-login {code}
    API->>WX: 验证code获取openid
    WX->>API: 返回用户信息
    API->>MP: 返回token和用户信息
```

### Token管理

- Token存储在本地Storage中
- 每次请求自动携带Token
- Token过期时自动刷新或重新登录

## 📊 数据同步策略

### 混合模式

1. **优先远程** - 有网络时优先使用API数据
2. **降级本地** - 网络异常时使用本地缓存
3. **后台同步** - 网络恢复时自动同步本地数据

### 同步队列

```javascript
// 离线时的操作会加入同步队列
const syncQueue = [
  { action: 'addFavorite', data: { storyId: 1 } },
  { action: 'recordReading', data: { storyId: 2 } }
];

// 网络恢复时自动处理队列
await processSyncQueue();
```

## 🛠️ 核心API接口

### 1. 认证接口

#### 微信登录
```http
POST /api/auth/wx-login
Content-Type: application/json

{
  "code": "wx_login_code"
}
```

响应：
```json
{
  "code": 0,
  "data": {
    "token": "jwt_token",
    "userInfo": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar": "头像URL"
    }
  }
}
```

### 2. 故事接口

#### 获取故事列表
```http
GET /api/stories?page=1&pageSize=10&category=adventure
```

响应：
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "title": "小橘猫的冒险",
      "emoji": "🐱",
      "content": "故事内容...",
      "category": "adventure",
      "readCount": 1250,
      "favoriteCount": 89,
      "publishTime": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 10
}
```

#### 获取故事详情
```http
GET /api/stories/1
```

#### 搜索故事
```http
GET /api/stories/search?keyword=猫咪&page=1&pageSize=10
```

#### 记录阅读
```http
POST /api/stories/read
Content-Type: application/json

{
  "storyId": 1,
  "readTime": "2024-01-15T10:00:00Z"
}
```

### 3. 用户接口

#### 获取用户信息
```http
GET /api/user/info
Authorization: Bearer <token>
```

#### 获取收藏列表
```http
GET /api/user/favorites?page=1&pageSize=10
Authorization: Bearer <token>
```

#### 添加收藏
```http
POST /api/user/favorites
Authorization: Bearer <token>
Content-Type: application/json

{
  "storyId": 1
}
```

#### 取消收藏
```http
DELETE /api/user/favorites/1
Authorization: Bearer <token>
```

#### 同步本地数据
```http
POST /api/user/sync
Authorization: Bearer <token>
Content-Type: application/json

{
  "favorites": [1, 2, 3],
  "readStories": [1, 2, 3, 4],
  "searchHistory": ["猫咪", "冒险"],
  "theme": "light"
}
```

## 🔄 离线支持

### 缓存策略

1. **故事列表** - 缓存5分钟
2. **故事详情** - 缓存30分钟
3. **用户数据** - 实时同步

### 离线操作

```javascript
// 离线时的操作会被记录
await dataManager.addFavorite(storyId); // 自动处理离线情况

// 网络恢复时自动同步
wx.onNetworkStatusChange((res) => {
  if (res.isConnected) {
    dataManager.processSyncQueue();
  }
});
```

## 🚨 错误处理

### 错误类型

1. **网络错误** - 连接失败、超时等
2. **HTTP错误** - 4xx、5xx状态码
3. **业务错误** - 接口返回的业务错误码

### 错误处理策略

```javascript
try {
  const result = await api.get('/api/stories');
} catch (error) {
  if (error.type === 'NETWORK_ERROR') {
    // 使用本地缓存
    return getLocalData();
  } else if (error.code === 401) {
    // 重新登录
    redirectToLogin();
  } else {
    // 显示错误信息
    showError(error.message);
  }
}
```

## 📈 性能优化

### 1. 请求优化

- 使用请求缓存
- 实现请求去重
- 支持请求取消

### 2. 数据优化

- 分页加载
- 懒加载
- 数据预加载

### 3. 网络优化

- 请求重试机制
- 智能降级策略
- 离线数据同步

## 🔒 安全考虑

### 1. 数据安全

- 敏感数据加密存储
- HTTPS传输
- Token定期刷新

### 2. 接口安全

- 请求签名验证
- 频率限制
- 参数校验

## 📱 小程序特殊处理

### 1. 网络限制

- 只能请求配置的域名
- 需要在小程序后台配置服务器域名

### 2. 存储限制

- 本地存储有大小限制
- 合理使用缓存策略

### 3. 生命周期

- 处理小程序的前后台切换
- 合理的数据同步时机

## 🧪 测试建议

### 1. 网络测试

- 正常网络环境
- 弱网络环境
- 离线环境

### 2. 数据测试

- 数据同步测试
- 缓存测试
- 错误恢复测试

### 3. 用户体验测试

- 加载速度测试
- 交互响应测试
- 错误提示测试

## 📝 部署清单

### 上线前检查

- [ ] API地址配置正确
- [ ] 服务器域名已配置
- [ ] 错误处理完善
- [ ] 离线功能正常
- [ ] 性能测试通过
- [ ] 安全检查完成

### 监控指标

- API响应时间
- 错误率
- 用户活跃度
- 数据同步成功率

---

通过以上配置，你的猫咪故事杂志小程序就可以完美接入后端API，实现云端数据同步和管理了！🎉
