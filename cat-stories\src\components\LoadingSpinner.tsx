interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="relative">
        {/* Spinning cat emoji */}
        <div className={`${sizeClasses[size]} animate-spin text-pink-500`}>
          <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray="60"
              strokeDashoffset="60"
              className="animate-pulse"
            />
          </svg>
        </div>
        
        {/* Cat face in center */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-lg">🐱</span>
        </div>
      </div>
    </div>
  );
}
