'use client';

import { useEffect } from 'react';
import ShareButton from './ShareButton';

interface CatStory {
  id: number;
  title: string;
  content: string;
  emoji: string;
  color: string;
}

interface StoryModalProps {
  story: CatStory | null;
  onClose: () => void;
}

export default function StoryModal({ story, onClose }: StoryModalProps) {
  useEffect(() => {
    if (story) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [story]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (story) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [story, onClose]);

  if (!story) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200"
      onClick={onClose}
    >
      <div
        className="bg-white dark:bg-gray-900 max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-in zoom-in-95 duration-200 border-4 border-black dark:border-white"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Magazine Article Header */}
        <header className="border-b-2 border-black dark:border-white p-8">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-2">
                特别故事
              </div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-black text-black dark:text-white mb-4 leading-tight">
                {story.title}
              </h1>
              <div className="text-center mb-4">
                <div className="text-6xl">{story.emoji}</div>
              </div>
            </div>

            <div className="flex items-center gap-2 ml-4">
              <ShareButton
                title={`${story.title}`}
                text={story.content.substring(0, 100) + '...'}
              />
              <button
                onClick={onClose}
                className="text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 text-2xl font-bold w-10 h-10 flex items-center justify-center transition-all duration-200"
                aria-label="关闭故事"
              >
                ×
              </button>
            </div>
          </div>
        </header>

        {/* Magazine Article Content */}
        <article className="p-8">
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-800 dark:text-gray-200 leading-relaxed text-lg font-serif first-letter:text-6xl first-letter:font-black first-letter:text-black dark:first-letter:text-white first-letter:float-left first-letter:mr-3 first-letter:mt-2 first-letter:leading-none">
              {story.content}
            </p>
          </div>

          {/* Article End */}
          <div className="mt-12 pt-8 border-t border-gray-300 dark:border-gray-600 text-center">
            <div className="inline-block bg-black dark:bg-white text-white dark:text-black px-6 py-2 font-bold uppercase tracking-wide text-sm mb-4">
              故事完
            </div>
            <p className="text-gray-600 dark:text-gray-400 text-sm italic">
              "每一个故事都是一段温暖的旅程"
            </p>
          </div>
        </article>
      </div>
    </div>
  );
}
