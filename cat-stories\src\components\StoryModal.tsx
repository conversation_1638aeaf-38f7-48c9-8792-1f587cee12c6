'use client';

import { useEffect } from 'react';

interface CatStory {
  id: number;
  title: string;
  content: string;
  emoji: string;
  color: string;
}

interface StoryModalProps {
  story: CatStory | null;
  onClose: () => void;
}

export default function StoryModal({ story, onClose }: StoryModalProps) {
  useEffect(() => {
    if (story) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [story]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (story) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [story, onClose]);

  if (!story) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-in fade-in duration-200"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto animate-in zoom-in-95 duration-200"
        onClick={(e) => e.stopPropagation()}
      >
        <div className={`${story.color} p-6 rounded-t-2xl relative`}>
          {/* Decorative elements */}
          <div className="absolute top-4 right-16 opacity-30 text-2xl">🌟</div>
          <div className="absolute top-8 right-8 opacity-30 text-xl">💫</div>
          
          <div className="flex justify-between items-center relative z-10">
            <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
              <span className="text-4xl animate-pulse">{story.emoji}</span>
              <span>{story.title}</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-600 hover:text-gray-800 text-3xl font-bold w-10 h-10 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-50 transition-all duration-200"
              aria-label="关闭故事"
            >
              ×
            </button>
          </div>
        </div>
        
        <div className="p-6">
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 leading-relaxed text-lg first-letter:text-4xl first-letter:font-bold first-letter:text-purple-600 first-letter:float-left first-letter:mr-2 first-letter:mt-1">
              {story.content}
            </p>
          </div>
          
          {/* Story end decoration */}
          <div className="text-center mt-8 pt-6 border-t border-gray-200">
            <div className="text-2xl mb-2">🐾 故事结束 🐾</div>
            <p className="text-gray-500 text-sm">希望你喜欢这个温馨的猫咪故事</p>
          </div>
        </div>
      </div>
    </div>
  );
}
