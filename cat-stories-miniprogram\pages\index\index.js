// pages/index/index.js
const app = getApp();

Page({
  data: {
    theme: 'light',
    searchQuery: '',
    showFavoritesOnly: false,
    currentPage: 0,
    storiesPerPage: 3,
    allStories: [],
    filteredStories: [],
    currentStories: [],
    totalPages: 0,
    favorites: []
  },

  onLoad() {
    this.initData();
  },

  onShow() {
    // 每次显示页面时更新数据
    this.setData({
      theme: app.globalData.theme,
      favorites: app.globalData.favorites
    });
    this.updateStories();
  },

  initData() {
    const { theme, favorites, stories } = app.globalData;
    this.setData({
      theme,
      favorites,
      allStories: stories,
      filteredStories: stories
    });
    this.updateStories();
  },

  updateStories() {
    let stories = this.data.filteredStories;
    
    // 应用收藏筛选
    if (this.data.showFavoritesOnly) {
      stories = stories.filter(story => this.data.favorites.includes(story.id));
    }

    // 计算分页
    const totalPages = Math.ceil(stories.length / this.data.storiesPerPage);
    const startIndex = this.data.currentPage * this.data.storiesPerPage;
    const currentStories = stories.slice(startIndex, startIndex + this.data.storiesPerPage);

    this.setData({
      currentStories,
      totalPages,
      filteredStories: stories
    });
  },

  // 搜索
  onSearchInput(e) {
    const query = e.detail.value;
    const filteredStories = app.searchStories(query);
    
    this.setData({
      searchQuery: query,
      filteredStories,
      currentPage: 0
    });
    
    this.updateStories();
  },

  // 主题切换
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      theme: app.globalData.theme
    });
  },

  // 收藏切换
  toggleFavorite(e) {
    const storyId = parseInt(e.currentTarget.dataset.id);
    app.toggleFavorite(storyId);
    
    this.setData({
      favorites: app.globalData.favorites
    });
    
    this.updateStories();
  },

  // 收藏筛选
  toggleFavoritesFilter() {
    this.setData({
      showFavoritesOnly: !this.data.showFavoritesOnly,
      currentPage: 0
    });
    this.updateStories();
  },

  // 分页
  prevPage() {
    if (this.data.currentPage > 0) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.updateStories();
    }
  },

  nextPage() {
    if (this.data.currentPage < this.data.totalPages - 1) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.updateStories();
    }
  },

  // 随机故事
  randomStory() {
    const stories = this.data.filteredStories;
    if (stories.length === 0) return;
    
    const randomIndex = Math.floor(Math.random() * stories.length);
    const randomStory = stories[randomIndex];
    
    this.readStory({ currentTarget: { dataset: { story: randomStory } } });
  },

  // 阅读故事
  readStory(e) {
    const story = e.currentTarget.dataset.story;
    wx.navigateTo({
      url: `/pages/story-detail/story-detail?id=${story.id}`
    });
  },

  // 导航到目录
  goToTableOfContents() {
    wx.navigateTo({
      url: '/pages/table-of-contents/table-of-contents'
    });
  },

  // 导航到封面
  goToMagazineCover() {
    wx.navigateTo({
      url: '/pages/magazine-cover/magazine-cover'
    });
  },

  // 清除筛选
  clearFilters() {
    this.setData({
      searchQuery: '',
      showFavoritesOnly: false,
      currentPage: 0,
      filteredStories: this.data.allStories
    });
    this.updateStories();
  },

  // 判断是否收藏
  isFavorite(storyId) {
    return this.data.favorites.includes(storyId);
  },

  // 主题更新回调
  updateTheme(theme) {
    this.setData({ theme });
  }
});
