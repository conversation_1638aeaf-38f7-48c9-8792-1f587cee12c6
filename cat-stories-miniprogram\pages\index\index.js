// pages/index/index.js
const app = getApp();

Page({
  data: {
    theme: 'light',
    searchQuery: '',
    showFavoritesOnly: false,
    currentPage: 0,
    storiesPerPage: 3,
    allStories: [],
    filteredStories: [],
    currentStories: [],
    totalPages: 0,
    favorites: [],
    searchHistory: []
  },

  onLoad() {
    this.initData();
    this.showWelcomeMessage();
  },

  onShow() {
    // 每次显示页面时更新数据
    this.setData({
      theme: app.globalData.theme,
      favorites: app.globalData.favorites
    });
    this.updateStories();
  },

  initData() {
    const { theme, favorites, stories } = app.globalData;
    const searchHistory = wx.getStorageSync('searchHistory') || [];
    this.setData({
      theme,
      favorites,
      allStories: stories,
      filteredStories: stories,
      searchHistory
    });
    this.updateStories();
  },

  updateStories() {
    let stories = this.data.filteredStories;
    
    // 应用收藏筛选
    if (this.data.showFavoritesOnly) {
      stories = stories.filter(story => this.data.favorites.includes(story.id));
    }

    // 计算分页
    const totalPages = Math.ceil(stories.length / this.data.storiesPerPage);
    const startIndex = this.data.currentPage * this.data.storiesPerPage;
    const currentStories = stories.slice(startIndex, startIndex + this.data.storiesPerPage);

    this.setData({
      currentStories,
      totalPages,
      filteredStories: stories
    });
  },

  // 搜索
  onSearchInput(e) {
    const query = e.detail.value;
    const filteredStories = app.searchStories(query);

    // 保存搜索历史
    if (query && query.trim()) {
      this.saveSearchHistory(query.trim());
    }

    this.setData({
      searchQuery: query,
      filteredStories,
      currentPage: 0
    });

    this.updateStories();
  },

  // 保存搜索历史
  saveSearchHistory(query) {
    let history = this.data.searchHistory;

    // 移除重复项
    history = history.filter(item => item !== query);

    // 添加到开头
    history.unshift(query);

    // 限制历史记录数量
    if (history.length > 5) {
      history = history.slice(0, 5);
    }

    this.setData({ searchHistory: history });
    wx.setStorageSync('searchHistory', history);
  },

  // 主题切换
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      theme: app.globalData.theme
    });
  },

  // 收藏切换
  toggleFavorite(e) {
    const storyId = parseInt(e.currentTarget.dataset.id);
    const isFavorite = app.toggleFavorite(storyId);

    this.setData({
      favorites: app.globalData.favorites
    });

    // 显示提示信息
    wx.showToast({
      title: isFavorite ? '已添加到收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500
    });

    this.updateStories();
  },

  // 收藏筛选
  toggleFavoritesFilter() {
    this.setData({
      showFavoritesOnly: !this.data.showFavoritesOnly,
      currentPage: 0
    });
    this.updateStories();
  },

  // 分页
  prevPage() {
    if (this.data.currentPage > 0) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.updateStories();
    }
  },

  nextPage() {
    if (this.data.currentPage < this.data.totalPages - 1) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.updateStories();
    }
  },

  // 随机故事
  randomStory() {
    const stories = this.data.filteredStories;
    if (stories.length === 0) {
      wx.showToast({
        title: '暂无可用故事',
        icon: 'none'
      });
      return;
    }

    const randomIndex = Math.floor(Math.random() * stories.length);
    const randomStory = stories[randomIndex];

    wx.showToast({
      title: `为您推荐：${randomStory.title}`,
      icon: 'none',
      duration: 2000
    });

    setTimeout(() => {
      this.readStory({ currentTarget: { dataset: { story: randomStory } } });
    }, 500);
  },

  // 阅读故事
  readStory(e) {
    const story = e.currentTarget.dataset.story;
    wx.navigateTo({
      url: `/pages/story-detail/story-detail?id=${story.id}`
    });
  },

  // 导航到目录
  goToTableOfContents() {
    wx.switchTab({
      url: '/pages/table-of-contents/table-of-contents'
    });
  },

  // 导航到封面
  goToMagazineCover() {
    wx.switchTab({
      url: '/pages/magazine-cover/magazine-cover'
    });
  },

  // 清除筛选
  clearFilters() {
    this.setData({
      searchQuery: '',
      showFavoritesOnly: false,
      currentPage: 0,
      filteredStories: this.data.allStories
    });
    this.updateStories();
  },

  // 判断是否收藏
  isFavorite(storyId) {
    return this.data.favorites.includes(storyId);
  },

  // 下拉刷新
  onPullDownRefresh() {
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 1000
    });

    // 重新初始化数据
    setTimeout(() => {
      this.initData();
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新完成',
        icon: 'success',
        duration: 1500
      });
    }, 1000);
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '猫咪故事杂志 - 温馨可爱的猫咪世界',
      desc: '9个精彩的猫咪故事，适合全家阅读',
      path: '/pages/index/index'
    };
  },

  // 显示欢迎消息
  showWelcomeMessage() {
    const isFirstTime = !wx.getStorageSync('hasVisited');
    if (isFirstTime) {
      wx.showModal({
        title: '欢迎来到猫咪故事杂志！🐱',
        content: '这里有9个温馨可爱的猫咪故事等你来探索。你可以收藏喜欢的故事，随时回来阅读。',
        showCancel: false,
        confirmText: '开始阅读',
        success: () => {
          wx.setStorageSync('hasVisited', true);
        }
      });
    }
  },

  // 主题更新回调
  updateTheme(theme) {
    this.setData({ theme });
  }
});
