// pages/index/index.js
import dataManager from '../../utils/dataManager.js';

const app = getApp();

Page({
  data: {
    theme: 'light',
    searchQuery: '',
    showFavoritesOnly: false,
    currentPage: 0,
    storiesPerPage: 3,
    allStories: [],
    filteredStories: [],
    currentStories: [],
    totalPages: 0,
    favorites: [],
    searchHistory: [],
    loading: false,
    hasMore: true,
    isOnline: true,
    activeButton: '' // 当前激活的按钮
  },

  async onLoad() {
    await this.initData();
    this.showWelcomeMessage();
    this.checkNetworkStatus();

    // 初始化按钮状态
    this.setData({
      activeButton: this.data.showFavoritesOnly ? 'favorites' : ''
    });
  },

  onShow() {
    // 每次显示页面时更新数据
    this.setData({
      theme: app.globalData.theme,
      favorites: app.globalData.favorites,
      // 重置按钮状态（从其他页面返回时）
      activeButton: this.data.showFavoritesOnly ? 'favorites' : ''
    });
    this.updateStories();
  },

  async initData() {
    const { theme, favorites } = app.globalData;
    const searchHistory = wx.getStorageSync('searchHistory') || [];

    this.setData({
      theme,
      favorites,
      searchHistory,
      loading: true
    });

    // 从API获取故事列表
    await this.loadStoriesFromAPI();
  },

  // 从API加载故事
  async loadStoriesFromAPI() {
    try {
      this.setData({ loading: true });

      const result = await app.getStoriesFromAPI({
        page: this.data.currentPage + 1,
        pageSize: this.data.storiesPerPage
      });

      if (result.success) {
        const stories = result.data || [];

        this.setData({
          allStories: stories,
          filteredStories: stories,
          loading: false,
          isOnline: !result.fromCache
        });

        this.updateStories();

        // 如果是从缓存加载，显示提示
        if (result.fromCache) {
          wx.showToast({
            title: '离线模式',
            icon: 'none',
            duration: 1500
          });
        }
      } else {
        throw new Error(result.error || '加载失败');
      }
    } catch (error) {
      console.error('加载故事失败:', error);

      // 降级到本地数据
      const { stories } = app.globalData;
      this.setData({
        allStories: stories,
        filteredStories: stories,
        loading: false,
        isOnline: false
      });

      this.updateStories();

      wx.showToast({
        title: '网络异常，使用离线数据',
        icon: 'none',
        duration: 2000
      });
    }
  },

  updateStories() {
    let stories = this.data.filteredStories;
    
    // 应用收藏筛选
    if (this.data.showFavoritesOnly) {
      stories = stories.filter(story => this.data.favorites.includes(story.id));
    }

    // 计算分页
    const totalPages = Math.ceil(stories.length / this.data.storiesPerPage);
    const startIndex = this.data.currentPage * this.data.storiesPerPage;
    const currentStories = stories.slice(startIndex, startIndex + this.data.storiesPerPage);

    this.setData({
      currentStories,
      totalPages,
      filteredStories: stories
    });
  },

  // 搜索
  async onSearchInput(e) {
    const query = e.detail.value;

    // 保存搜索历史
    if (query && query.trim()) {
      this.saveSearchHistory(query.trim());
    }

    this.setData({
      searchQuery: query,
      currentPage: 0,
      loading: true
    });

    if (query.trim()) {
      // 使用API搜索
      await this.searchStoriesFromAPI(query.trim());
    } else {
      // 重新加载所有故事
      await this.loadStoriesFromAPI();
    }
  },

  // 从API搜索故事
  async searchStoriesFromAPI(keyword) {
    try {
      const result = await app.searchStoriesFromAPI(keyword, {
        page: 1,
        pageSize: 20
      });

      if (result.success) {
        this.setData({
          filteredStories: result.data || [],
          loading: false
        });

        this.updateStories();
      } else {
        throw new Error(result.error || '搜索失败');
      }
    } catch (error) {
      console.error('搜索失败:', error);

      // 降级到本地搜索
      const filteredStories = app.searchStories(keyword);
      this.setData({
        filteredStories,
        loading: false
      });

      this.updateStories();
    }
  },

  // 保存搜索历史
  saveSearchHistory(query) {
    let history = this.data.searchHistory;

    // 移除重复项
    history = history.filter(item => item !== query);

    // 添加到开头
    history.unshift(query);

    // 限制历史记录数量
    if (history.length > 5) {
      history = history.slice(0, 5);
    }

    this.setData({ searchHistory: history });
    wx.setStorageSync('searchHistory', history);
  },

  // 主题切换
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      theme: app.globalData.theme
    });
  },

  // 收藏切换
  toggleFavorite(e) {
    const storyId = parseInt(e.currentTarget.dataset.id);
    const isFavorite = app.toggleFavorite(storyId);

    this.setData({
      favorites: app.globalData.favorites
    });

    // 显示提示信息
    wx.showToast({
      title: isFavorite ? '已添加到收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500
    });

    this.updateStories();
  },

  // 收藏筛选
  toggleFavoritesFilter() {
    const newShowFavoritesOnly = !this.data.showFavoritesOnly;
    this.setActiveButton(newShowFavoritesOnly ? 'favorites' : '');

    this.setData({
      showFavoritesOnly: newShowFavoritesOnly,
      currentPage: 0
    });
    this.updateStories();
  },

  // 分页
  prevPage() {
    if (this.data.currentPage > 0) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.updateStories();
    }
  },

  nextPage() {
    if (this.data.currentPage < this.data.totalPages - 1) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.updateStories();
    }
  },

  // 随机故事
  randomStory() {
    this.setActiveButton('random');

    const stories = this.data.filteredStories;
    if (stories.length === 0) {
      wx.showToast({
        title: '暂无可用故事',
        icon: 'none'
      });
      return;
    }

    const randomIndex = Math.floor(Math.random() * stories.length);
    const randomStory = stories[randomIndex];

    wx.showToast({
      title: `为您推荐：${randomStory.title}`,
      icon: 'none',
      duration: 2000
    });

    setTimeout(() => {
      this.readStory({ currentTarget: { dataset: { story: randomStory } } });
    }, 500);
  },

  // 阅读故事
  readStory(e) {
    const story = e.currentTarget.dataset.story;
    wx.navigateTo({
      url: `/pages/story-detail/story-detail?id=${story.id}`
    });
  },

  // 导航到目录
  goToTableOfContents() {
    this.setActiveButton('contents');
    wx.switchTab({
      url: '/pages/table-of-contents/table-of-contents'
    });
  },

  // 导航到封面
  goToMagazineCover() {
    this.setActiveButton('cover');
    wx.switchTab({
      url: '/pages/magazine-cover/magazine-cover'
    });
  },

  // 清除筛选
  clearFilters() {
    this.setData({
      searchQuery: '',
      showFavoritesOnly: false,
      currentPage: 0,
      filteredStories: this.data.allStories
    });
    this.updateStories();
  },

  // 判断是否收藏
  isFavorite(storyId) {
    return this.data.favorites.includes(storyId);
  },

  // 检查网络状态
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        const isOnline = res.networkType !== 'none';
        this.setData({ isOnline });

        if (!isOnline) {
          wx.showToast({
            title: '网络连接异常',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.setData({ isOnline: res.isConnected });

      if (res.isConnected) {
        wx.showToast({
          title: '网络已连接',
          icon: 'success',
          duration: 1500
        });
        // 网络恢复时重新加载数据
        this.loadStoriesFromAPI();
      } else {
        wx.showToast({
          title: '网络连接断开',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      // 重置页面状态
      this.setData({
        currentPage: 0,
        searchQuery: '',
        showFavoritesOnly: false
      });

      // 重新加载数据
      await this.loadStoriesFromAPI();

      wx.showToast({
        title: '刷新完成',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 1500
      });
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  // 上拉加载更多
  async onReachBottom() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const result = await app.getStoriesFromAPI({
        page: this.data.currentPage + 2, // 下一页
        pageSize: this.data.storiesPerPage
      });

      if (result.success && result.data.length > 0) {
        const newStories = [...this.data.allStories, ...result.data];
        this.setData({
          allStories: newStories,
          filteredStories: newStories,
          currentPage: this.data.currentPage + 1,
          loading: false
        });

        this.updateStories();
      } else {
        this.setData({
          hasMore: false,
          loading: false
        });

        wx.showToast({
          title: '没有更多故事了',
          icon: 'none',
          duration: 1500
        });
      }
    } catch (error) {
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '猫咪故事杂志 - 温馨可爱的猫咪世界',
      desc: '9个精彩的猫咪故事，适合全家阅读',
      path: '/pages/index/index'
    };
  },

  // 显示欢迎消息
  showWelcomeMessage() {
    const isFirstTime = !wx.getStorageSync('hasVisited');
    if (isFirstTime) {
      wx.showModal({
        title: '欢迎来到猫咪故事杂志！🐱',
        content: '这里有9个温馨可爱的猫咪故事等你来探索。你可以收藏喜欢的故事，随时回来阅读。',
        showCancel: false,
        confirmText: '开始阅读',
        success: () => {
          wx.setStorageSync('hasVisited', true);
        }
      });
    }
  },

  // 设置活跃按钮
  setActiveButton(buttonType) {
    this.setData({
      activeButton: buttonType
    });

    // 短暂显示后清除焦点状态（除了收藏按钮）
    if (buttonType && buttonType !== 'favorites') {
      setTimeout(() => {
        this.setData({
          activeButton: this.data.showFavoritesOnly ? 'favorites' : ''
        });
      }, 1000);
    }
  },

  // 主题更新回调
  updateTheme(theme) {
    this.setData({ theme });
  }
});
