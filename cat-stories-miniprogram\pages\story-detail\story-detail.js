// pages/story-detail/story-detail.js
const app = getApp();

Page({
  data: {
    theme: 'light',
    story: null,
    isFavorite: false,
    prevStory: null,
    nextStory: null
  },

  onLoad(options) {
    const storyId = parseInt(options.id);
    this.loadStory(storyId);
    this.setData({
      theme: app.globalData.theme
    });
  },

  onShow() {
    // 更新收藏状态
    if (this.data.story) {
      this.setData({
        isFavorite: app.isFavorite(this.data.story.id),
        theme: app.globalData.theme
      });
    }
  },

  loadStory(storyId) {
    const story = app.getStoryById(storyId);
    if (!story) {
      wx.showToast({
        title: '故事不存在',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 获取前后故事
    const stories = app.globalData.stories;
    const currentIndex = stories.findIndex(s => s.id === storyId);
    const prevStory = currentIndex > 0 ? stories[currentIndex - 1] : null;
    const nextStory = currentIndex < stories.length - 1 ? stories[currentIndex + 1] : null;

    this.setData({
      story,
      isFavorite: app.isFavorite(storyId),
      prevStory,
      nextStory
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: story.title
    });
  },

  // 主题切换
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      theme: app.globalData.theme
    });
  },

  // 收藏切换
  toggleFavorite() {
    if (!this.data.story) return;
    
    const isFavorite = app.toggleFavorite(this.data.story.id);
    this.setData({
      isFavorite
    });

    wx.showToast({
      title: isFavorite ? '已添加到收藏' : '已取消收藏',
      icon: 'success'
    });
  },

  // 分享故事
  shareStory() {
    const { story } = this.data;
    if (!story) return;

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 也可以复制到剪贴板
    wx.setClipboardData({
      data: `${story.title}\n\n${story.content.substring(0, 100)}...\n\n来自《猫咪故事杂志》`,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 随机下一篇
  randomNext() {
    const stories = app.globalData.stories;
    const currentId = this.data.story.id;
    const otherStories = stories.filter(s => s.id !== currentId);
    
    if (otherStories.length === 0) return;
    
    const randomIndex = Math.floor(Math.random() * otherStories.length);
    const randomStory = otherStories[randomIndex];
    
    this.loadStory(randomStory.id);
  },

  // 导航到指定故事
  goToStory(e) {
    const storyId = parseInt(e.currentTarget.dataset.id);
    this.loadStory(storyId);
  },

  // 返回
  goBack() {
    wx.navigateBack();
  },

  // 分享配置
  onShareAppMessage() {
    const { story } = this.data;
    if (!story) return {};

    return {
      title: story.title,
      desc: story.content.substring(0, 100) + '...',
      path: `/pages/story-detail/story-detail?id=${story.id}`,
      imageUrl: '' // 可以设置分享图片
    };
  },

  onShareTimeline() {
    const { story } = this.data;
    if (!story) return {};

    return {
      title: `${story.title} - 猫咪故事杂志`,
      query: `id=${story.id}`,
      imageUrl: '' // 可以设置分享图片
    };
  },

  // 主题更新回调
  updateTheme(theme) {
    this.setData({ theme });
  }
});
