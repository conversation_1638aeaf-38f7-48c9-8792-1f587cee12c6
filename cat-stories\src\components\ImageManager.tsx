'use client';

import { useState } from 'react';
import Image from 'next/image';

interface ImageManagerProps {
  storyId: number;
  title: string;
  alt: string;
  className?: string;
  showPlaceholder?: boolean;
}

// 图片文件映射
const imageMap: Record<number, string> = {
  1: '/images/story-1-orange-cat-adventure.png',
  2: '/images/story-2-white-cat-moonlight.png',
  3: '/images/story-3-black-cat-garden.png',
  4: '/images/story-4-gray-cat-sleeping.png',
  5: '/images/story-5-tabby-cat-hero.png',
  6: '/images/story-6-calico-cat-fish.png',
  7: '/images/story-7-music-cat-piano.png',
  8: '/images/story-8-art-cat-painting.png',
  9: '/images/story-9-scholar-cat-books.png',
};

export default function ImageManager({ 
  storyId, 
  title, 
  alt, 
  className = "",
  showPlaceholder = true 
}: ImageManagerProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  
  const imagePath = imageMap[storyId];
  
  // 如果没有对应的图片路径或图片加载失败，显示占位符
  if (!imagePath || imageError) {
    if (!showPlaceholder) return null;
    
    return (
      <div className={`relative bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center ${className}`}>
        <div className="text-center p-4">
          <div className="text-4xl mb-2">🎨</div>
          <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">
            Eric Carle风格插画
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
            {title}
          </div>
        </div>
        
        {/* 装饰元素 */}
        <div className="absolute top-2 left-2 w-4 h-4 bg-orange-300 opacity-50 rounded-full"></div>
        <div className="absolute top-4 right-3 w-3 h-3 bg-blue-300 opacity-50 rounded-full"></div>
        <div className="absolute bottom-3 left-4 w-5 h-2 bg-green-300 opacity-50 rounded-full"></div>
        <div className="absolute bottom-2 right-2 w-2 h-4 bg-pink-300 opacity-50 rounded-full"></div>
      </div>
    );
  }
  
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {imageLoading && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl mb-2">🎨</div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              加载中...
            </div>
          </div>
        </div>
      )}
      
      <Image
        src={imagePath}
        alt={alt}
        fill
        className="object-cover"
        onLoad={() => setImageLoading(false)}
        onError={() => {
          setImageError(true);
          setImageLoading(false);
        }}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
      
      {/* Eric Carle风格边框效果 */}
      <div className="absolute inset-0 border-2 border-dashed border-white opacity-20 pointer-events-none"></div>
    </div>
  );
}
