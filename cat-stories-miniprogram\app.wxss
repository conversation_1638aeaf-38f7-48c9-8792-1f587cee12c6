/* app.wxss - 全局样式 */

/* 基础重置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #000000;
  background-color: #ffffff;
}

/* 主题变量 - 温馨猫咪主题配色 */
.theme-light {
  --bg-color: #fefcf8;
  --text-color: #2d2926;
  --text-color-rgb: 45, 41, 38;
  --border-color: #d4c4b0;
  --card-bg: #ffffff;
  --secondary-text: #8b7355;
  --hover-bg: #f7f3ed;
  --shadow: rgba(139, 115, 85, 0.15);
  --accent-color: #e67e22;
  --accent-light: #f39c12;
  --warm-orange: #ff9f43;
  --soft-brown: #a0826d;
  --cream: #faf6f0;
  --border-radius: 12rpx;
  --border-radius-small: 8rpx;
  --border-radius-large: 16rpx;
}

.theme-dark {
  --bg-color: #1a1612;
  --text-color: #f4f1eb;
  --text-color-rgb: 244, 241, 235;
  --border-color: #4a3f35;
  --card-bg: #2c241d;
  --secondary-text: #b8a690;
  --hover-bg: #332920;
  --shadow: rgba(244, 241, 235, 0.1);
  --accent-color: #ff9f43;
  --accent-light: #ffa726;
  --warm-orange: #ffb74d;
  --soft-brown: #8d6e63;
  --cream: #2c241d;
  --border-radius: 12rpx;
  --border-radius-small: 8rpx;
  --border-radius-large: 16rpx;
}

/* 容器样式 */
.container {
  padding: 24rpx 20rpx;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* 杂志风格标题 */
.magazine-title {
  font-size: 72rpx;
  font-weight: 900;
  text-align: center;
  margin-bottom: 16rpx;
  letter-spacing: 4rpx;
  text-transform: uppercase;
}

.magazine-subtitle {
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 64rpx;
  letter-spacing: 2rpx;
  color: var(--secondary-text);
}

/* 按钮样式 - 温馨圆角设计 */
.btn {
  padding: 28rpx 48rpx;
  border: 3rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-weight: 600;
  letter-spacing: 1rpx;
  font-size: 28rpx;
  text-align: center;
  border-radius: var(--border-radius);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx var(--shadow);
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
  color: #ffffff;
  border-color: var(--accent-color);
  box-shadow: 0 4rpx 12rpx rgba(var(--text-color-rgb), 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--accent-light) 0%, var(--warm-orange) 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(var(--text-color-rgb), 0.25);
}

.btn:hover {
  background-color: var(--hover-bg);
  border-color: var(--accent-color);
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx var(--shadow);
}

/* 卡片样式 - 温馨圆角设计 */
.story-card {
  border: 3rpx solid var(--border-color);
  background-color: var(--card-bg);
  margin-bottom: 32rpx;
  padding: 32rpx 24rpx;
  border-radius: var(--border-radius);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx var(--shadow);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
}

.story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--warm-orange) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.story-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 12rpx 32rpx rgba(var(--text-color-rgb), 0.15);
  border-color: var(--accent-color);
}

.story-card:hover::before {
  opacity: 1;
}

.story-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(var(--text-color-rgb), 0.12);
}

/* 图片容器 - 温馨圆角设计 */
.image-container {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(135deg, var(--cream) 0%, var(--hover-bg) 100%);
  border: 3rpx solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 2rpx 8rpx rgba(var(--text-color-rgb), 0.05);
}

.theme-dark .image-container {
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--hover-bg) 100%);
}

.story-emoji {
  font-size: 120rpx;
  line-height: 1;
}

.image-placeholder {
  text-align: center;
  color: var(--secondary-text);
}

.image-placeholder-text {
  font-size: 24rpx;
  margin-top: 16rpx;
  font-style: italic;
}

/* 特稿样式 */
.featured-story {
  border-bottom: 4rpx solid var(--border-color);
  padding-bottom: 64rpx;
  margin-bottom: 64rpx;
}

.featured-image {
  height: 600rpx;
}

.featured-emoji {
  font-size: 200rpx;
}

/* 网格布局 */
.stories-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32rpx;
}

@media (min-width: 768rpx) {
  .stories-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 搜索框 - 改进的可读性和圆角设计 */
.search-container {
  margin-bottom: 48rpx;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 28rpx 32rpx;
  border: 3rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.5;
  border-radius: var(--border-radius);
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx var(--shadow);
  min-height: 88rpx;
}

.search-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 4rpx 16rpx rgba(var(--text-color-rgb), 0.15);
  outline: none;
}

.search-input::placeholder {
  color: var(--secondary-text);
  font-size: 26rpx;
  font-weight: 400;
}

.search-placeholder {
  color: var(--secondary-text) !important;
  font-size: 26rpx !important;
  font-weight: 400 !important;
  opacity: 0.9;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.modal-content {
  background-color: var(--bg-color);
  border: 4rpx solid var(--border-color);
  max-width: 800rpx;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  padding: 32rpx;
  border-bottom: 2rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 32rpx;
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  border: 2rpx solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;
  margin-top: 64rpx;
  padding: 32rpx 16rpx;
  box-sizing: border-box;
  width: 100%;
  overflow-x: auto;
}

.pagination-btn {
  padding: 16rpx 24rpx;
  border: 3rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 24rpx;
  font-weight: 500;
  min-width: 72rpx;
  text-align: center;
  border-radius: var(--border-radius-small);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 6rpx var(--shadow);
  box-sizing: border-box;
}

.pagination-btn:disabled {
  opacity: 0.5;
  color: var(--secondary-text);
  border-color: var(--secondary-text);
}

.pagination-info {
  font-size: 24rpx;
  color: var(--secondary-text);
}

/* 收藏按钮 - 温馨圆角设计 */
.favorite-btn {
  width: 80rpx;
  height: 80rpx;
  border: 3rpx solid var(--border-color);
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx var(--shadow);
}

.favorite-btn:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
  transform: scale(1.1);
  box-shadow: 0 4rpx 16rpx rgba(var(--text-color-rgb), 0.2);
}

.favorite-btn.active {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

/* 主题切换按钮 - 温馨圆角设计 */
.theme-toggle {
  position: fixed;
  top: 24rpx;
  right: 24rpx;
  width: 72rpx;
  height: 72rpx;
  border: 3rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 28rpx;
  border-radius: var(--border-radius);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx var(--shadow);
}

.theme-toggle:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(var(--text-color-rgb), 0.25);
}

/* 响应式设计 - 全局优化 */
@media (max-width: 768rpx) {
  .container {
    padding: 20rpx 16rpx;
  }

  .search-input {
    padding: 24rpx 28rpx;
    font-size: 26rpx;
    min-height: 80rpx;
  }

  .search-placeholder {
    font-size: 24rpx !important;
  }

  .story-card {
    padding: 28rpx 20rpx;
    margin-bottom: 24rpx;
  }

  .pagination {
    gap: 16rpx;
    padding: 24rpx 12rpx;
  }

  .pagination-btn {
    padding: 12rpx 20rpx;
    font-size: 22rpx;
    min-width: 64rpx;
  }

  .theme-toggle {
    width: 64rpx;
    height: 64rpx;
    font-size: 24rpx;
    top: 20rpx;
    right: 20rpx;
  }
}

/* 超小屏幕优化 */
@media (max-width: 600rpx) {
  .container {
    padding: 16rpx 12rpx;
  }

  .search-input {
    padding: 20rpx 24rpx;
    font-size: 24rpx;
    min-height: 72rpx;
  }

  .story-card {
    padding: 24rpx 16rpx;
  }

  .pagination {
    gap: 12rpx;
    padding: 20rpx 8rpx;
  }

  .pagination-btn {
    padding: 10rpx 16rpx;
    font-size: 20rpx;
    min-width: 56rpx;
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-bold { font-weight: bold; }
.text-uppercase { text-transform: uppercase; }
.mb-16 { margin-bottom: 16rpx; }
.mb-32 { margin-bottom: 32rpx; }
.mb-48 { margin-bottom: 48rpx; }
.mb-64 { margin-bottom: 64rpx; }
.mt-32 { margin-top: 32rpx; }
.p-32 { padding: 32rpx; }

/* 加载动画 */
.loading-animation {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 提示样式 */
.toast-success {
  background-color: #52c41a;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.toast-error {
  background-color: #ff4d4f;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 响应式 */
@media (max-width: 768rpx) {
  .magazine-title {
    font-size: 56rpx;
  }

  .featured-image {
    height: 400rpx;
  }

  .featured-emoji {
    font-size: 160rpx;
  }

  .container {
    padding: 24rpx;
  }
}
