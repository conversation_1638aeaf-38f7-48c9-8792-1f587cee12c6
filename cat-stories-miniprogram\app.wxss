/* app.wxss - 全局样式 */

/* 基础重置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #000000;
  background-color: #ffffff;
}

/* 主题变量 */
.theme-light {
  --bg-color: #ffffff;
  --text-color: #000000;
  --border-color: #000000;
  --card-bg: #ffffff;
  --secondary-text: #666666;
  --hover-bg: #f5f5f5;
  --shadow: rgba(0, 0, 0, 0.1);
}

.theme-dark {
  --bg-color: #000000;
  --text-color: #ffffff;
  --border-color: #ffffff;
  --card-bg: #111111;
  --secondary-text: #cccccc;
  --hover-bg: #222222;
  --shadow: rgba(255, 255, 255, 0.1);
}

/* 容器样式 */
.container {
  padding: 32rpx;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* 杂志风格标题 */
.magazine-title {
  font-size: 72rpx;
  font-weight: 900;
  text-align: center;
  margin-bottom: 16rpx;
  letter-spacing: 4rpx;
  text-transform: uppercase;
}

.magazine-subtitle {
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 64rpx;
  letter-spacing: 2rpx;
  color: var(--secondary-text);
}

/* 按钮样式 */
.btn {
  padding: 24rpx 48rpx;
  border: 4rpx solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  font-weight: bold;
  letter-spacing: 2rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--text-color);
  color: var(--bg-color);
}

.btn-primary:hover {
  background-color: var(--secondary-text);
}

.btn:hover {
  background-color: var(--text-color);
  color: var(--bg-color);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx var(--shadow);
}

/* 卡片样式 */
.story-card {
  border: 4rpx solid var(--border-color);
  background-color: var(--card-bg);
  margin-bottom: 32rpx;
  padding: 32rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.story-card:hover {
  box-shadow: 12rpx 12rpx 0 var(--border-color);
  transform: translate(-6rpx, -6rpx);
}

.story-card:active {
  transform: translate(-2rpx, -2rpx);
  box-shadow: 4rpx 4rpx 0 var(--border-color);
}

/* 图片容器 */
.image-container {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  border: 4rpx solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
}

.theme-dark .image-container {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
}

.story-emoji {
  font-size: 120rpx;
  line-height: 1;
}

.image-placeholder {
  text-align: center;
  color: var(--secondary-text);
}

.image-placeholder-text {
  font-size: 24rpx;
  margin-top: 16rpx;
  font-style: italic;
}

/* 特稿样式 */
.featured-story {
  border-bottom: 4rpx solid var(--border-color);
  padding-bottom: 64rpx;
  margin-bottom: 64rpx;
}

.featured-image {
  height: 600rpx;
}

.featured-emoji {
  font-size: 200rpx;
}

/* 网格布局 */
.stories-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32rpx;
}

@media (min-width: 768rpx) {
  .stories-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 搜索框 */
.search-container {
  margin-bottom: 48rpx;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 28rpx;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: var(--secondary-text);
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.modal-content {
  background-color: var(--bg-color);
  border: 4rpx solid var(--border-color);
  max-width: 800rpx;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  padding: 32rpx;
  border-bottom: 2rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 32rpx;
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  border: 2rpx solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32rpx;
  margin-top: 64rpx;
  padding: 32rpx 0;
}

.pagination-btn {
  padding: 16rpx 32rpx;
  border: 2rpx solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  font-size: 24rpx;
  min-width: 80rpx;
  text-align: center;
}

.pagination-btn:disabled {
  opacity: 0.5;
  color: var(--secondary-text);
  border-color: var(--secondary-text);
}

.pagination-info {
  font-size: 24rpx;
  color: var(--secondary-text);
}

/* 收藏按钮 */
.favorite-btn {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid var(--border-color);
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.2s ease;
}

.favorite-btn:hover {
  background-color: var(--text-color);
  transform: scale(1.1);
}

/* 主题切换按钮 */
.theme-toggle {
  position: fixed;
  top: 32rpx;
  right: 32rpx;
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 32rpx;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 工具类 */
.text-center { text-align: center; }
.text-bold { font-weight: bold; }
.text-uppercase { text-transform: uppercase; }
.mb-16 { margin-bottom: 16rpx; }
.mb-32 { margin-bottom: 32rpx; }
.mb-48 { margin-bottom: 48rpx; }
.mb-64 { margin-bottom: 64rpx; }
.mt-32 { margin-top: 32rpx; }
.p-32 { padding: 32rpx; }

/* 加载动画 */
.loading-animation {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 提示样式 */
.toast-success {
  background-color: #52c41a;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.toast-error {
  background-color: #ff4d4f;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 响应式 */
@media (max-width: 768rpx) {
  .magazine-title {
    font-size: 56rpx;
  }

  .featured-image {
    height: 400rpx;
  }

  .featured-emoji {
    font-size: 160rpx;
  }

  .container {
    padding: 24rpx;
  }
}
