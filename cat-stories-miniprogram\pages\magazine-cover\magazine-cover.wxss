/* pages/magazine-cover/magazine-cover.wxss */

/* 杂志封面容器 */
.magazine-cover {
  max-width: 600rpx;
  margin: 0 auto 64rpx;
  border: 8rpx solid var(--border-color);
  background-color: var(--card-bg);
  padding: 48rpx;
  position: relative;
  box-shadow: 16rpx 16rpx 0 var(--shadow);
}

/* 封面头部 */
.cover-header {
  text-align: center;
  margin-bottom: 48rpx;
  border-bottom: 4rpx solid var(--border-color);
  padding-bottom: 32rpx;
}

.cover-title {
  font-size: 56rpx;
  font-weight: 900;
  margin-bottom: 16rpx;
  color: var(--text-color);
  letter-spacing: 4rpx;
}

.cover-subtitle {
  font-size: 24rpx;
  font-weight: bold;
  color: var(--secondary-text);
  letter-spacing: 8rpx;
  text-transform: uppercase;
  margin-bottom: 16rpx;
}

.cover-issue {
  font-size: 20rpx;
  color: var(--secondary-text);
  letter-spacing: 2rpx;
}

/* 主封面图片 */
.cover-image-main {
  height: 480rpx;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
  border: 4rpx solid var(--border-color);
  margin-bottom: 48rpx;
  position: relative;
  overflow: hidden;
}

.theme-dark .cover-image-main {
  background: linear-gradient(135deg, #cc7a44 0%, #cc5529 50%, #c7741a 100%);
}

.cover-illustration {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.cover-emoji {
  font-size: 160rpx;
  margin-bottom: 24rpx;
  text-shadow: 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.cover-illustration-text {
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
  letter-spacing: 2rpx;
}

/* 特色内容 */
.cover-features {
  margin-bottom: 48rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
  padding: 16rpx;
  border-left: 4rpx solid var(--border-color);
  background-color: var(--hover-bg);
}

.feature-emoji {
  font-size: 32rpx;
}

.feature-text {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
}

/* 故事预览 */
.cover-stories-preview {
  margin-bottom: 48rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: var(--text-color);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 2rpx;
}

.preview-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  border: 2rpx solid var(--border-color);
  background-color: var(--bg-color);
}

.preview-emoji {
  font-size: 24rpx;
}

.preview-story-title {
  font-size: 20rpx;
  color: var(--text-color);
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 封面底部 */
.cover-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  border-top: 2rpx solid var(--border-color);
  padding-top: 24rpx;
}

.cover-price {
  font-size: 32rpx;
  font-weight: 900;
  color: var(--text-color);
  background-color: var(--text-color);
  color: var(--bg-color);
  padding: 8rpx 16rpx;
  transform: rotate(-5deg);
}

.cover-barcode {
  text-align: right;
}

.barcode-lines {
  display: flex;
  gap: 4rpx;
  margin-bottom: 8rpx;
  justify-content: flex-end;
}

.barcode-line {
  width: 4rpx;
  height: 40rpx;
  background-color: var(--text-color);
}

.barcode-line:nth-child(2n) {
  width: 2rpx;
}

.barcode-number {
  font-size: 16rpx;
  color: var(--secondary-text);
  font-family: monospace;
}

/* 操作按钮 */
.cover-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

/* 响应式 */
@media (max-width: 768rpx) {
  .magazine-cover {
    max-width: 100%;
    margin: 0 0 48rpx;
    padding: 32rpx;
  }
  
  .cover-title {
    font-size: 48rpx;
  }
  
  .cover-image-main {
    height: 360rpx;
  }
  
  .cover-emoji {
    font-size: 120rpx;
  }
  
  .cover-illustration-text {
    font-size: 24rpx;
  }
  
  .preview-list {
    grid-template-columns: 1fr;
  }
  
  .cover-actions {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .cover-actions .btn {
    flex: 1;
    min-width: 200rpx;
  }
}
