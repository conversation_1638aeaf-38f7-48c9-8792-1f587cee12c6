// pages/login/login.js
import userService from '../../services/userService.js';

const app = getApp();

Page({
  data: {
    theme: 'light',
    loginLoading: false
  },

  onLoad() {
    this.setData({
      theme: app.globalData.theme
    });
  },

  /**
   * 获取用户信息并登录
   */
  async onGetUserInfo(e) {
    if (e.detail.errMsg !== 'getUserInfo:ok') {
      wx.showToast({
        title: '需要授权才能登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({ loginLoading: true });

    try {
      // 获取微信登录code
      const loginResult = await this.getWxLoginCode();
      if (!loginResult.success) {
        throw new Error('获取登录凭证失败');
      }

      // 调用登录API
      const result = await userService.wxLogin(loginResult.code);
      
      if (result.success) {
        // 更新全局用户信息
        app.globalData.userInfo = result.data.userInfo;
        app.globalData.isLoggedIn = true;

        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          this.navigateBack();
        }, 1500);

      } else {
        throw new Error(result.error || '登录失败');
      }

    } catch (error) {
      console.error('登录失败:', error);
      wx.showModal({
        title: '登录失败',
        content: error.message || '网络异常，请稍后重试',
        showCancel: false,
        confirmText: '确定'
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  /**
   * 获取微信登录code
   */
  getWxLoginCode() {
    return new Promise((resolve) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            resolve({ success: true, code: res.code });
          } else {
            resolve({ success: false, error: '获取code失败' });
          }
        },
        fail: (error) => {
          resolve({ success: false, error: error.errMsg });
        }
      });
    });
  },

  /**
   * 游客模式登录
   */
  guestLogin() {
    wx.showModal({
      title: '游客模式',
      content: '游客模式下无法同步数据到云端，确定继续吗？',
      confirmText: '继续',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 设置游客模式标识
          wx.setStorageSync('isGuest', true);
          
          wx.showToast({
            title: '已进入游客模式',
            icon: 'success',
            duration: 1500
          });

          setTimeout(() => {
            this.navigateBack();
          }, 1500);
        }
      }
    });
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护。我们只会收集必要的信息用于提供更好的服务，不会泄露您的个人信息。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '使用本应用即表示您同意遵守相关使用规范，我们将为您提供优质的阅读体验。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 主题更新回调
   */
  updateTheme(theme) {
    this.setData({ theme });
  }
});
