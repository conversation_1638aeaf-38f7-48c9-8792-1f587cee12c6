/* pages/login/login.wxss */

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 64rpx 32rpx;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 96rpx;
}

.app-logo {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  animation: bounce 2s infinite;
}

.app-title {
  font-size: 48rpx;
  font-weight: 900;
  color: var(--text-color);
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: var(--secondary-text);
  letter-spacing: 1rpx;
}

/* 登录表单 */
.login-form {
  width: 100%;
  max-width: 600rpx;
}

.login-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-color);
  text-align: center;
  margin-bottom: 16rpx;
}

.login-desc {
  font-size: 28rpx;
  color: var(--secondary-text);
  text-align: center;
  margin-bottom: 64rpx;
  line-height: 1.5;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 96rpx;
  margin-bottom: 32rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.wx-login-btn {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
}

.wx-login-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(7, 193, 96, 0.4);
}

.guest-btn {
  background-color: transparent;
  border: 4rpx solid var(--border-color);
  color: var(--text-color);
}

.guest-btn:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2rpx);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
}

/* 登录提示 */
.login-tips {
  margin-top: 48rpx;
  padding: 32rpx;
  background-color: var(--hover-bg);
  border-radius: 12rpx;
  border-left: 8rpx solid var(--text-color);
}

.tip-item {
  font-size: 28rpx;
  color: var(--text-color);
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 隐私政策 */
.privacy-notice {
  position: fixed;
  bottom: 64rpx;
  left: 32rpx;
  right: 32rpx;
  text-align: center;
  font-size: 24rpx;
  color: var(--secondary-text);
  line-height: 1.5;
}

.privacy-notice .link {
  color: var(--text-color);
  text-decoration: underline;
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

/* 加载状态 */
.login-btn[loading] {
  opacity: 0.7;
  pointer-events: none;
}

/* 响应式 */
@media (max-width: 768rpx) {
  .login-header {
    margin-bottom: 64rpx;
  }
  
  .app-logo {
    font-size: 96rpx;
  }
  
  .app-title {
    font-size: 40rpx;
  }
  
  .login-btn {
    height: 88rpx;
  }
  
  .btn-text {
    font-size: 28rpx;
  }
}
