<!-- pages/magazine-cover/magazine-cover.wxml -->
<view class="container theme-{{theme}}">
  <!-- 主题切换按钮 -->
  <view class="theme-toggle" bindtap="toggleTheme">
    {{theme === 'light' ? '🌙' : '☀️'}}
  </view>

  <!-- 杂志封面 -->
  <view class="magazine-cover">
    <!-- 杂志标题 -->
    <view class="cover-header">
      <view class="cover-title">猫咪故事杂志</view>
      <view class="cover-subtitle">CAT STORIES MAGAZINE</view>
      <view class="cover-issue">第一期 · 2024年冬季刊</view>
    </view>

    <!-- 主封面图片 -->
    <view class="cover-image-main">
      <view class="cover-illustration">
        <view class="cover-emoji">🐱</view>
        <view class="cover-illustration-text">温馨猫咪故事专辑</view>
      </view>
    </view>

    <!-- 特色内容预览 -->
    <view class="cover-features">
      <view class="feature-item">
        <view class="feature-emoji">🌟</view>
        <view class="feature-text">精选9个温馨猫咪故事</view>
      </view>
      <view class="feature-item">
        <view class="feature-emoji">🎨</view>
        <view class="feature-text">Eric Carle风格精美插画</view>
      </view>
      <view class="feature-item">
        <view class="feature-emoji">❤️</view>
        <view class="feature-text">温馨治愈，适合全家阅读</view>
      </view>
    </view>

    <!-- 故事预览 -->
    <view class="cover-stories-preview">
      <view class="preview-title">本期精彩内容</view>
      <view class="preview-list">
        <view wx:for="{{featuredStories}}" wx:key="id" class="preview-item">
          <view class="preview-emoji">{{item.emoji}}</view>
          <view class="preview-story-title">{{item.title}}</view>
        </view>
      </view>
    </view>

    <!-- 封面底部信息 -->
    <view class="cover-footer">
      <view class="cover-price">免费阅读</view>
      <view class="cover-barcode">
        <view class="barcode-lines">
          <view class="barcode-line"></view>
          <view class="barcode-line"></view>
          <view class="barcode-line"></view>
          <view class="barcode-line"></view>
          <view class="barcode-line"></view>
        </view>
        <view class="barcode-number">9 771234 567890</view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="cover-actions">
    <button class="btn btn-primary" bindtap="startReading">
      📖 开始阅读故事
    </button>
    <button class="btn" bindtap="viewTableOfContents">
      📚 查看完整目录
    </button>
    <button class="btn" bindtap="shareApp">
      📤 分享给朋友
    </button>
  </view>
</view>
