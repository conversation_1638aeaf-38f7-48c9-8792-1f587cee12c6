{"name": "jest-resolve-dependencies", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-resolve-dependencies"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"jest-regex-util": "30.0.1", "jest-snapshot": "30.0.4"}, "devDependencies": {"@jest/test-utils": "30.0.4", "@jest/types": "30.0.1", "jest-haste-map": "30.0.2", "jest-resolve": "30.0.2", "jest-runtime": "30.0.4"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5"}