<!-- pages/table-of-contents/table-of-contents.wxml -->
<view class="container theme-{{theme}}">
  <!-- 主题切换按钮 -->
  <view class="theme-toggle" bindtap="toggleTheme">
    {{theme === 'light' ? '🌙' : '☀️'}}
  </view>

  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">目录</view>
    <view class="page-subtitle">猫咪故事杂志 - 全部故事</view>
  </view>

  <!-- 筛选选项 -->
  <view class="filter-options">
    <button 
      class="filter-btn {{!showFavoritesOnly ? 'active' : ''}}" 
      bindtap="showAllStories"
    >
      全部故事 ({{allStories.length}})
    </button>
    <button 
      class="filter-btn {{showFavoritesOnly ? 'active' : ''}}" 
      bindtap="showFavoriteStories"
    >
      我的收藏 ({{favorites.length}})
    </button>
  </view>

  <!-- 故事列表 -->
  <view class="stories-list">
    <view 
      wx:for="{{displayStories}}" 
      wx:key="id"
      class="story-item"
      bindtap="readStory"
      data-story="{{item}}"
    >
      <view class="story-number">{{index + 1 < 10 ? '0' + (index + 1) : index + 1}}</view>
      
      <view class="story-thumbnail">
        <view class="story-emoji-small">{{item.emoji}}</view>
      </view>
      
      <view class="story-info">
        <view class="story-title-small">{{item.title}}</view>
        <view class="story-preview">{{item.content.substring(0, 100)}}...</view>
      </view>
      
      <view class="story-meta">
        <button 
          class="favorite-btn-small" 
          bindtap="toggleFavorite" 
          data-id="{{item.id}}"
          catchtap="true"
        >
          {{isFavorite(item.id) ? '❤️' : '🤍'}}
        </button>
        <view class="read-indicator">阅读 →</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{displayStories.length === 0}}" class="empty-state">
    <view class="empty-emoji">📚</view>
    <view class="empty-text">
      {{showFavoritesOnly ? '还没有收藏任何故事' : '暂无故事'}}
    </view>
    <button wx:if="{{showFavoritesOnly}}" class="btn" bindtap="showAllStories">
      浏览全部故事
    </button>
  </view>

  <!-- 统计信息 -->
  <view class="stats">
    <view class="stats-item">
      <view class="stats-number">{{allStories.length}}</view>
      <view class="stats-label">总故事数</view>
    </view>
    <view class="stats-item">
      <view class="stats-number">{{favorites.length}}</view>
      <view class="stats-label">已收藏</view>
    </view>
    <view class="stats-item">
      <view class="stats-number">{{readingProgress}}%</view>
      <view class="stats-label">阅读进度</view>
    </view>
  </view>
</view>
