// pages/magazine-cover/magazine-cover.js
const app = getApp();

Page({
  data: {
    theme: 'light',
    featuredStories: []
  },

  onLoad() {
    this.initData();
  },

  onShow() {
    this.setData({
      theme: app.globalData.theme
    });
  },

  initData() {
    // 获取前6个故事作为特色内容
    const stories = app.globalData.stories.slice(0, 6);
    this.setData({
      theme: app.globalData.theme,
      featuredStories: stories
    });
  },

  // 主题切换
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      theme: app.globalData.theme
    });
  },

  // 开始阅读
  startReading() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 查看目录
  viewTableOfContents() {
    wx.switchTab({
      url: '/pages/table-of-contents/table-of-contents'
    });
  },

  // 分享应用
  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 复制分享文案到剪贴板
    const shareText = `《猫咪故事杂志》- 温馨可爱的猫咪世界\n\n收录9个精彩的猫咪故事，配有Eric Carle风格插画，适合全家一起阅读。快来体验这个充满爱与温暖的猫咪世界吧！`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '分享文案已复制',
          icon: 'success'
        });
      }
    });
  },

  // 分享配置
  onShareAppMessage() {
    return {
      title: '猫咪故事杂志 - 温馨可爱的猫咪世界',
      desc: '9个精彩的猫咪故事，Eric Carle风格插画，适合全家阅读',
      path: '/pages/magazine-cover/magazine-cover',
      imageUrl: '' // 可以设置封面分享图片
    };
  },

  onShareTimeline() {
    return {
      title: '猫咪故事杂志 - 温馨可爱的猫咪世界',
      query: '',
      imageUrl: '' // 可以设置封面分享图片
    };
  },

  // 主题更新回调
  updateTheme(theme) {
    this.setData({ theme });
  }
});
