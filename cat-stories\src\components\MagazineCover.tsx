'use client';

export default function MagazineCover() {
  return (
    <div className="bg-white dark:bg-gray-900 border-4 border-black dark:border-white p-8 max-w-md mx-auto">
      {/* Magazine Header */}
      <div className="text-center mb-6">
        <div className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400 mb-1">
          第1期 • 2024年冬季刊
        </div>
        <h1 className="text-4xl font-black uppercase tracking-tight text-black dark:text-white mb-2">
          CAT STORIES
        </h1>
        <div className="text-xs font-bold uppercase tracking-widest text-gray-600 dark:text-gray-400">
          MAGAZINE
        </div>
      </div>

      {/* Main Cover Illustration */}
      <div className="relative mb-6 h-64 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900 dark:to-orange-800 border-2 border-black dark:border-white">
        {/* <PERSON> style cover illustration */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-8xl mb-2">🐱</div>
            <div className="text-sm font-bold text-black dark:text-white">
              温馨猫咪故事专辑
            </div>
          </div>
        </div>
        
        {/* Decorative Eric Carle style elements */}
        <div className="absolute top-4 left-4 w-12 h-8 bg-blue-300 opacity-70 rounded-full transform rotate-12"></div>
        <div className="absolute top-8 right-6 w-8 h-12 bg-green-300 opacity-70 rounded-full transform -rotate-12"></div>
        <div className="absolute bottom-6 left-8 w-16 h-6 bg-pink-300 opacity-70 rounded-full transform rotate-45"></div>
        <div className="absolute bottom-4 right-4 w-6 h-16 bg-yellow-300 opacity-70 rounded-full transform -rotate-45"></div>
        
        {/* Texture overlay */}
        <div className="absolute inset-0 opacity-30 bg-gradient-to-br from-transparent via-white to-transparent mix-blend-overlay"></div>
      </div>

      {/* Cover Stories */}
      <div className="space-y-2 mb-6">
        <div className="text-xs font-bold uppercase tracking-wide text-black dark:text-white">
          本期精选
        </div>
        <div className="text-xs text-gray-700 dark:text-gray-300 leading-relaxed">
          • 小橘猫的冒险之旅<br/>
          • 月光下的白猫漫步<br/>
          • 花园里的小黑猫<br/>
          • 会弹钢琴的音乐猫<br/>
          • 爱画画的艺术猫<br/>
          • 还有更多温馨故事...
        </div>
      </div>

      {/* Magazine Footer */}
      <div className="border-t border-black dark:border-white pt-4 text-center">
        <div className="text-xs font-bold uppercase tracking-widest text-gray-500 dark:text-gray-400">
          Eric Carle风格插画 • 9篇精选故事
        </div>
      </div>
    </div>
  );
}
